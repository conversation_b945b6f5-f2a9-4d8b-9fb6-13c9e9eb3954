package com.nymbl.tenant.service;

import com.google.common.base.Strings;
import com.nymbl.config.Constants;
import com.nymbl.config.clearingHouse.Waystar837P;
import com.nymbl.config.clearingHouse.WaystarAPI;
import com.nymbl.config.dto.*;
import com.nymbl.config.dto.reports.IRxSales;
import com.nymbl.config.dto.reports.RxSalesReport;
import com.nymbl.config.exception.X12Exception;
import com.nymbl.config.model.FullName;
import com.nymbl.config.service.AbstractTableService;
import com.nymbl.config.service.TableObjectContainer;
import com.nymbl.config.service.VersionExceptionLockInterface;
import com.nymbl.config.utils.DateUtil;
import com.nymbl.config.utils.OptimisticLockingUtil;
import com.nymbl.config.utils.StringUtil;
import com.nymbl.config.x12.x835.Factory835;
import com.nymbl.config.x12.x837.Factory837;
import com.nymbl.config.x12.x837.Factory837Parameters;
import com.nymbl.master.model.ClearingHousePayer;
import com.nymbl.master.model.Company;
import com.nymbl.master.model.User;
import com.nymbl.master.service.ClearingHousePayerService;
import com.nymbl.master.service.CompanyService;
import com.nymbl.master.service.UserService;
import com.nymbl.tenant.Auditable;
import com.nymbl.tenant.TenantContext;
import com.nymbl.tenant.Timed;
import com.nymbl.tenant.dashboard.dto.BranchDto;
import com.nymbl.tenant.dashboard.dto.InsuranceVerification_L_CodeDto;
import com.nymbl.tenant.dashboard.dto.PrescriptionDto;
import com.nymbl.tenant.dashboard.repository.BranchDtoRepository;
import com.nymbl.tenant.dashboard.repository.InsuranceVerificationDtoRepository;
import com.nymbl.tenant.dashboard.service.PrescriptionDtoService;
import com.nymbl.tenant.interfaces.IClaimTotals;
import com.nymbl.tenant.model.*;
import com.nymbl.tenant.model.interfaces.ClaimInfo;
import com.nymbl.tenant.repository.AppliedPaymentRepository;
import com.nymbl.tenant.repository.AppliedPayment_L_CodeRepository;
import com.nymbl.tenant.repository.ClaimRepository;
import com.nymbl.tenant.repository.DeliveryLocationRepository;
import com.nymbl.config.x12.x837.X12Claim;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.map.HashedMap;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.*;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.text.DecimalFormat;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.nymbl.config.Constants.DF_YYYY_MM_DD_HH_MM;
import static com.nymbl.config.utils.OptimisticLockingUtil.SAVED;
import static org.codehaus.groovy.runtime.DefaultGroovyMethods.contains;
import static org.springframework.data.domain.ExampleMatcher.GenericPropertyMatchers.exact;

/**
 * Created by vbi on 6/16/17.
 */
@Slf4j
@Service
public class ClaimService extends AbstractTableService<Claim, Long> implements VersionExceptionLockInterface<Claim> {

    private final ClaimRepository claimRepository;
    private final UserService userService;
    private final PrescriptionDiagnosisCodeService prescriptionDiagnosisCodeService;
    private final InsuranceVerificationService insuranceVerificationService;
    private final InsuranceVerificationLCodeService insuranceVerificationLCodeService;
    private final ClaimSubmissionService claimSubmissionService;
    private final SystemSettingService systemSettingService;
    private final FinancialResponsibilityService financialResponsibilityService;
    private final PrescriptionService prescriptionService;
    private final NotificationService notificationService;
    private final ClaimFileService claimFileService;
    private final InsuranceCompanyService insuranceCompanyService;
    private final PatientInsuranceService patientInsuranceService;
    private final TaskService taskService;
    private final AppliedPaymentRepository appliedPaymentRepository;
    private final DeliveryLocationRepository deliveryLocationRepository;
    private final AppliedPayment_L_CodeRepository appliedPaymentLCodeRepository;
    private final PrescriptionSectionService prescriptionSectionService;
    private final BranchService branchService;
    private final Factory837 factory837;
    private final AutoPostClaimResponseService autoPostClaimResponseService;
    private final PaymentService paymentService;
    private final AppliedPaymentService appliedPaymentService;
    private final AutoPostPatientService autoPostPatientService;
    private final PatientStatementService patientStatementService;
    private final NymblStatusHistoryService nymblStatusHistoryService;
    private final PrescriptionDtoService prescriptionDtoService;
    private final BranchDtoRepository branchDtoRepository;
    private final InsuranceVerificationDtoRepository insuranceVerificationDtoRepository;
    private final WaystarAPI waystarAPI;
    private final ClearingHousePayerService clearingHousePayerService;
    private final OptimisticLockingUtil optimisticLockingUtil;
    private final GeneralLedgerLiveService generalLedgerLiveService;
    private final FeatureFlagService featureFlagService;
    private final PhysicianService physicianService;
    private final GeneralLedgerService2 generalLedgerService2;
    private final ItemPhysicalService itemPhysicalService;
    private final Factory835 factory835;
    private final X12FileService x12FileService;
    private final NymblStatusService nymblStatusService;
    private BulkClaimJobService bulkClaimJobService;



    private String checkForError = null;

    @PersistenceContext(unitName = "tenant")
    private EntityManager entityManager;

    @Value("${upload.directory}")
    String uploadDirectory;

    @Autowired
    public ClaimService(ClaimRepository claimRepository,
                        UserService userService,
                        PrescriptionDiagnosisCodeService prescriptionDiagnosisCodeService,
                        @Lazy InsuranceVerificationService insuranceVerificationService,
                        InsuranceVerificationLCodeService insuranceVerificationLCodeService,
                        ClaimSubmissionService claimSubmissionService,
                        SystemSettingService systemSettingService,
                        FinancialResponsibilityService financialResponsibilityService,
                        @Lazy PrescriptionService prescriptionService,
                        @Lazy NotificationService notificationService,
                        ClaimFileService claimFileService,
                        InsuranceCompanyService insuranceCompanyService,
                        @Lazy PatientInsuranceService patientInsuranceService,
                        TaskService taskService,
                        AppliedPaymentRepository appliedPaymentRepository,
                        AppliedPayment_L_CodeRepository appliedPaymentLCodeRepository,
                        DeliveryLocationRepository deliveryLocationRepository,
                        PrescriptionSectionService prescriptionSectionService,
                        BranchService branchService,
                        @Lazy Factory837 factory837,
                        AutoPostClaimResponseService autoPostClaimResponseService,
                        @Lazy PaymentService paymentService,
                        @Lazy AppliedPaymentService appliedPaymentService,
                        AutoPostPatientService autoPostPatientService,
                        PatientStatementService patientStatementService,
                        NymblStatusHistoryService nymblStatusHistoryService,
                        PrescriptionDtoService prescriptionDtoService,
                        BranchDtoRepository branchDtoRepository,
                        InsuranceVerificationDtoRepository insuranceVerificationDtoRepository,
                        WaystarAPI waystarAPI,
                        ClearingHousePayerService clearingHousePayerService,
                        OptimisticLockingUtil optimisticLockingUtil,
                        GeneralLedgerLiveService generalLedgerLiveService,
                        FeatureFlagService featureFlagService,
                        CompanyService companyService,
                        PhysicianService physicianService,
                        GeneralLedgerService2 generalLedgerService2,
                        @Lazy ItemPhysicalService itemPhysicalService,
                        @Lazy Factory835 factory835,
                        @Lazy X12FileService x12FileService,
                        NymblStatusService nymblStatusService) {
        super(claimRepository);
        this.claimRepository = claimRepository;
        this.userService = userService;
        this.prescriptionDiagnosisCodeService = prescriptionDiagnosisCodeService;
        this.insuranceVerificationService = insuranceVerificationService;
        this.insuranceVerificationLCodeService = insuranceVerificationLCodeService;
        this.claimSubmissionService = claimSubmissionService;
        this.systemSettingService = systemSettingService;
        this.financialResponsibilityService = financialResponsibilityService;
        this.prescriptionService = prescriptionService;
        this.notificationService = notificationService;
        this.claimFileService = claimFileService;
        this.insuranceCompanyService = insuranceCompanyService;
        this.taskService = taskService;
        this.patientInsuranceService = patientInsuranceService;
        this.appliedPaymentRepository = appliedPaymentRepository;
        this.appliedPaymentLCodeRepository = appliedPaymentLCodeRepository;
        this.deliveryLocationRepository = deliveryLocationRepository;
        this.prescriptionSectionService = prescriptionSectionService;
        this.branchService = branchService;
        this.factory837 = factory837;
        this.autoPostClaimResponseService = autoPostClaimResponseService;
        this.paymentService = paymentService;
        this.appliedPaymentService = appliedPaymentService;
        this.autoPostPatientService = autoPostPatientService;
        this.patientStatementService = patientStatementService;
        this.nymblStatusHistoryService = nymblStatusHistoryService;
        this.prescriptionDtoService = prescriptionDtoService;
        this.branchDtoRepository = branchDtoRepository;
        this.insuranceVerificationDtoRepository = insuranceVerificationDtoRepository;
        this.waystarAPI = waystarAPI;
        this.clearingHousePayerService = clearingHousePayerService;
        this.optimisticLockingUtil = optimisticLockingUtil;
        this.generalLedgerLiveService = generalLedgerLiveService;
        this.featureFlagService = featureFlagService;
        this.physicianService = physicianService;
        this.generalLedgerService2 = generalLedgerService2;
        this.itemPhysicalService = itemPhysicalService;
        this.factory835 = factory835;
        this.x12FileService = x12FileService;
        this.nymblStatusService = nymblStatusService;
    }

    @Autowired
    public void setBulkClaimJobService(@Lazy BulkClaimJobService bulkClaimJobService) {
        this.bulkClaimJobService = bulkClaimJobService;
    }

    /**
     * Store the bulk X12 file content and update claims to reference the bulk job.
     *
     * @param jobId the bulk claim job ID
     * @param x12Content the X12 file content
     * @param claimIds the list of claim IDs
     */
    public void storeBulkX12File(String jobId, String x12Content, List<Long> claimIds) {
        // Store X12 content in bulk_claim_job
        BulkClaimJob job = bulkClaimJobService.findByJobId(jobId);
        if (job != null) {
            job.setX12FileContent(x12Content);
            bulkClaimJobService.save(job);

            // Update claims to reference the bulk job
            for (Long claimId : claimIds) {
                Claim claim = findOne(claimId);
                if (claim != null) {
                    claim.setBulkClaimJobId(jobId);
                    claim.setBulkSubmission(true);

                    save(claim);
                }
            }
        }
    }

    @Override
    public void loadForeignKeys(Claim o) {
        if (o != null) {
            o.setUser(userService.getUserById(o.getUserId()));
            o.setCreatedBy(userService.getUserById(o.getCreatedById()));
            o.setUpdatedBy(userService.getUserById(o.getUpdatedById()));
            ClearingHousePayer chp = null;
            if (null != o.getResponsiblePatientInsurance()
                    && null != o.getResponsiblePatientInsurance().getInsuranceCompany()
                    && null != o.getResponsiblePatientInsurance().getInsuranceCompany().getClearingHousePayerId()) {
                chp = clearingHousePayerService.findOne(o.getResponsiblePatientInsurance().getInsuranceCompany().getClearingHousePayerId());
                o.getResponsiblePatientInsurance().getInsuranceCompany().setClearingHousePayer(chp);
            }
        }
    }

    public List<Claim> findByPrescriptionPatientId(Long patientId, Integer claimFor) {
        List<Claim> results;
        if (claimFor == 1) {
            results = claimRepository.findPatientClaim1(patientId);
        } else {
            results = claimRepository.findPatientClaim0(patientId);
        }
        loadForeignKeysForClaimResults(results);
        return results;
    }

    public List<Claim> findByPrescriptionId(Long prescriptionId) {
        List<Claim> results = claimRepository.findByPrescriptionId(prescriptionId);
        loadForeignKeysForClaimResults(results);
        return results;
    }

    public Claim findTopByPrescriptionIdOrderByIdAsc(Long prescriptionId) {
        Claim result = claimRepository.findTopByPrescriptionIdOrderByIdAsc(prescriptionId);
        loadForeignKeys(result);
        return result;
    }

    /**
     * Find all claims associated with a bulk claim job.
     *
     * @param bulkClaimJobId the bulk claim job ID
     * @return list of claims
     */
    public List<Claim> findByBulkClaimJobId(String bulkClaimJobId) {
        List<Claim> results = claimRepository.findByBulkClaimJobId(bulkClaimJobId);
        loadForeignKeysForClaimResults(results);
        return results;
    }

    public List<Claim> getClaimsByClaimSubmissionDate(Date startDate, Date endDate) {
        List<Claim> results = claimRepository.getClaimsByClaimSubmissionDate(startDate, endDate);
        loadForeignKeysForClaimResults(results);
        return results;
    }

    public List<Claim> getActiveClaimsByFirstClaimSubmissionDate(Date startDate, Date endDate) {
        List<Claim> results = claimRepository.findByActivePrescriptionPatientPrimaryBranchIdAndDateOfServiceBetween(null, startDate, endDate);
        loadForeignKeysForClaimResults(results);
        return results;
    }

    public List<Claim> findByAdditionalCommentIsNotNull() {
        List<Claim> results = claimRepository.findByAdditionalCommentIsNotNull();
        loadForeignKeysForClaimResults(results);
        return results;
    }

    public void loadForeignKeysForClaimResults(List<Claim> results) {
        for (Claim o : results) {
            loadForeignKeys(o);
        }
    }

    public List<Claim> getAllClaimsWithActivePrescriptionAndOutstandingBalanceByBranch(Long branchId) {
        List<Claim> results = claimRepository.getAllClaimsWithActivePrescriptionAndOutstandingBalanceByBranch(branchId);
        loadForeignKeysForClaimResults(results);
        return results;
    }

    public Claim addClaim(Long prescriptionId, Long userId, Long billingBranchId, java.sql.Date dateOfService, boolean resend) {
        List<InsuranceVerification> insuranceVerifications = insuranceVerificationService.findByPrescriptionId(prescriptionId);
        Claim claim = updateClaim(prescriptionId, userId, billingBranchId, dateOfService, resend);
        List<String> alertMessages = getAlertMessages(insuranceVerifications);
        if (!alertMessages.isEmpty()) {
            notificationService.convertAndSendUserNotification(notificationService.createClaimMessage(claim.getId(), "Claim #: ".concat(claim.getId().toString()).concat(", ").concat(alertMessages.toString())));
        }
        SystemSetting autoLockWIP = systemSettingService.findBySectionAndField("billing", "auto_lock_wip");
        if ("Y".equals(autoLockWIP.getValue())) {
            List<PrescriptionSection> sections = prescriptionSectionService.findByPrescriptionIdAndLockedIsFalse(prescriptionId);
            for (PrescriptionSection s : sections) {
                s.setLocked(true);
                s.setUserId(userService.getCurrentUser().getId());
                s.setUpdatedAt(new Timestamp(Calendar.getInstance().getTime().getTime()));
                prescriptionSectionService.save(s);
            }
        }
        // Action of writing after all normal process complete.
        if (!resend && featureFlagService.findFeatureFlagByFeature("live_gl") != null) {
            if (featureFlagService.findFeatureFlagByFeature("live_gl") != null) {
                try {
                    generalLedgerService2.insertUpdateSalesEntry(claim);
                } catch (Exception e) {
                    log.error(StringUtil.getExceptionAsString(e));
                }
            }
        }
        return claim;
    }

    private List<String> getAlertMessages(List<InsuranceVerification> insuranceVerifications) {
        List<String> alertMessages = new ArrayList<>();
        for (InsuranceVerification insuranceVerification : insuranceVerifications) {
            InsuranceCompany insuranceCompany = insuranceVerification.getPatientInsurance().getInsuranceCompany();
            if ((insuranceCompany.getAlert() != null && insuranceCompany.getAlert()) && insuranceCompany.getAlertMessage() != null) {
                alertMessages.add("INS - ".concat(insuranceCompany.getName()).concat(": ").concat(insuranceCompany.getAlertMessage()).concat(", "));
            }
        }
        return alertMessages;
    }

    public Claim updateClaim(Long prescriptionId, Long userId, Long billingBranchId, java.sql.Date dateOfService, boolean resend) {
//        boolean createManualClaimSubmission = false; // If patient is selfpay, automatically generate a manual claim_submission record so it will populate in patient statement search.
        List<Claim> claims = findByPrescriptionId(prescriptionId);

        if(prescriptionId == null){
            log.error("Tenant = " + TenantContext.getCurrentTenant() + ", User Id =" + userId + " attempting to create a claim without a prescription id");
            return null;
        }

        if (resend)
            claims.clear();

        Claim claim;
        if (claims.size() <= 1) {
            claim = claims.size() == 1 ? claims.get(0) : new Claim();
            if (claim.getId() == null) {
                FinancialResponsibility financialResponsibility = financialResponsibilityService.findByPrescriptionId(prescriptionId);
                Prescription prescription = prescriptionService.findOne(prescriptionId);
                InsuranceVerification iv = insuranceVerificationService.findFirstByPrescriptionIdAndCarrierTypeOrderByIdDesc(prescriptionId, Constants.PRIMARY);
                List<InsuranceVerification_L_Code> ivlcs = insuranceVerificationLCodeService.findByInsuranceVerificationId(iv.getId());
                List<ItemPhysical> physicalItems = itemPhysicalService.findByPrescription(prescriptionId, null);
                BigDecimal grandTotal = BigDecimal.ZERO;
                for (InsuranceVerification_L_Code ivlc : ivlcs) {
                    ivlc.setTotalCharge(ivlc.getBillingFee().multiply(new BigDecimal(ivlc.getPrescriptionLCode().getQuantity())));
                    ivlc.setTotalAllowable(ivlc.getAllowableFee().multiply(new BigDecimal(ivlc.getPrescriptionLCode().getQuantity())));
                    if (ivlc.getUseSalesTax()) {
                        grandTotal = grandTotal.add(ivlc.getSalesTax());
                    }
                    grandTotal = grandTotal.add(ivlc.getTotalCharge());
                    // ItemPhysical
                    ItemPhysical ip = null;
                    Long prescriptionLCodeId = ivlc.getPrescriptionLCodeId();
                    if (prescriptionLCodeId != null && prescriptionLCodeId > 0) {
                        if (!physicalItems.isEmpty()) {
                            Optional<ItemPhysical> oip = physicalItems.stream().filter(item -> prescriptionLCodeId.equals(item.getPrescriptionLCodeId())).findFirst();
                            if (oip.isPresent()) {
                                ip = oip.get();
                            }
                        }
                        if (ip != null) {
                            // Populate these fields in IVLC from the physical item
                            ivlc.setCost(ip.getCurrentValue());
                            ivlc.setMsrp(ip.getItem().getItemByManufacturer().getMsrp());
                        }
                    }
                    ivlc = insuranceVerificationLCodeService.save(ivlc);

                    // IVLC needs to be saved before the physical item, because I need a valid ivlcId to finalize the latter
                    if (ip != null) {
                        if (ip.getDateOfServiceStart() == null) {
                            ip.setDateOfServiceStart(dateOfService);
                        }
                        ip.setInsuranceVerificationLCodeId(ivlc.getId());
                        Prescription_L_Code plc = ivlc.getPrescriptionLCode();
                        if ((plc.getModifier1() != null && plc.getModifier1().equals("RR")) || (plc.getModifier2() != null && plc.getModifier2().equals("RR")) ||
                                (plc.getModifier3() != null && plc.getModifier3().equals("RR")) || (plc.getModifier4() != null && plc.getModifier4().equals("RR"))) {
                            // Rentals are billed at the end of a rental_cycle, so we increment the count
                            String depreciationUnit = ip.getDepreciationUnit();
                            if (depreciationUnit != null && depreciationUnit.equals("rental_cycle")) {
                                Long depreciationUnits = ip.getDepreciationUnitsCount();
                                if (depreciationUnits == null) depreciationUnits = 0L;
                                ip.setDepreciationUnitsCount(depreciationUnits + 1);
                            }
                            String rentalStatus = plc.getRentalStatus();
                            if (rentalStatus != null && rentalStatus.equals("IN")) {
                                ip.setStatus("cleaning");
                            } else {
                                ip.setStatus("rented");
                            }
                        } else {
                            ip.setStatus("delivered");
                        }
                        itemPhysicalService.saveForVersion(ip);
                    }
                }
                claim.setTotalClaimAmount(grandTotal);
                claim.setTotalClaimPaid(BigDecimal.ZERO);
                claim.setTotalClaimBalance(BigDecimal.ZERO);
                claim.setTotalPtResponsibilityAmount(BigDecimal.ZERO);
                claim.setTotalPtResponsibilityPaid(BigDecimal.ZERO);
                claim.setTotalPtResponsibilityBalance(BigDecimal.ZERO);
                Boolean isSelfPay = (financialResponsibility.getSelfPay() != null && financialResponsibility.getSelfPay());
//                        || (financialResponsibility.getCoInsurancePercentage() != null && financialResponsibility.getCoInsurancePercentage() == 100);
                if (isSelfPay) {
//                    createManualClaimSubmission = true;
                    claim.setTotalPtResponsibilityBalance(grandTotal);
                } else {
                    claim.setTotalClaimBalance(grandTotal);
                }
                claim.setPatientInsuranceId(iv.getPatientInsuranceId());
                claim.setResponsiblePatientInsuranceId(iv.getPatientInsuranceId());
                claim.setAcceptAssignment(true);
                claim.setNymblRcm(false);
                claim.setBillingBranchId(prescription.getBranchId());
                PatientInsurance otherPatientInsurance = generateOtherPatientInsurance(iv);
                claim.setOtherPatientInsuranceId(otherPatientInsurance != null ? otherPatientInsurance.getId() : null);
            }
        } else {
            claim = claims.get(claims.size() - 1);
        }
        if (userId != null)
            claim.setUserId(userId);

        if (dateOfService != null) {
            claim.setDateOfService(dateOfService);
        }
        if (billingBranchId != null) {
            claim.setBillingBranchId(billingBranchId);
        }
        claim.setPrescriptionId(prescriptionId);
        claim = save(claim);
        loadForeignKeys(claim);

        // Action of writing after all normal process complete.
        if (featureFlagService.findFeatureFlagByFeature("live_gl") != null) {
            try {
                generalLedgerService2.insertUpdateSalesEntry(claim);
            } catch (Exception e) {
                log.error(StringUtil.getExceptionAsString(e));
            }
        }

        return claim;
    }

    public PatientInsurance generateOtherPatientInsurance(InsuranceVerification iv) {
        InsuranceVerification otherPatientInsuranceVerification = null;
        PatientInsurance otherPatientInsurance = null;
        if (Constants.PRIMARY.equals(iv.getCarrierType())) {
            otherPatientInsuranceVerification = insuranceVerificationService.findFirstByPrescriptionIdAndCarrierTypeOrderByIdDesc(iv.getPrescriptionId(), Constants.SECONDARY);
        } else if (Constants.SECONDARY.equals(iv.getCarrierType())) {
            InsuranceVerification tertiaryPlan = insuranceVerificationService.findFirstByPrescriptionIdAndCarrierTypeOrderByIdDesc(iv.getPrescriptionId(), Constants.TERTIARY);
            otherPatientInsuranceVerification = tertiaryPlan != null ? tertiaryPlan : insuranceVerificationService.findFirstByPrescriptionIdAndCarrierTypeOrderByIdDesc(iv.getPrescriptionId(), Constants.PRIMARY);
        } else if (Constants.TERTIARY.equals(iv.getCarrierType())) {
            InsuranceVerification quaternaryPlan = insuranceVerificationService.findFirstByPrescriptionIdAndCarrierTypeOrderByIdDesc(iv.getPrescriptionId(), Constants.QUATERNARY);
            otherPatientInsuranceVerification = quaternaryPlan != null ? quaternaryPlan : insuranceVerificationService.findFirstByPrescriptionIdAndCarrierTypeOrderByIdDesc(iv.getPrescriptionId(), Constants.PRIMARY);
        } else if (Constants.QUATERNARY.equals(iv.getCarrierType())) {
            InsuranceVerification quinaryPlan = insuranceVerificationService.findFirstByPrescriptionIdAndCarrierTypeOrderByIdDesc(iv.getPrescriptionId(), Constants.QUINARY);
            otherPatientInsuranceVerification = quinaryPlan != null ? quinaryPlan : insuranceVerificationService.findFirstByPrescriptionIdAndCarrierTypeOrderByIdDesc(iv.getPrescriptionId(), Constants.PRIMARY);
        } else if (Constants.QUINARY.equals(iv.getCarrierType())) {
            InsuranceVerification senaryPlan = insuranceVerificationService.findFirstByPrescriptionIdAndCarrierTypeOrderByIdDesc(iv.getPrescriptionId(), Constants.SENARY);
            otherPatientInsuranceVerification = senaryPlan != null ? senaryPlan : insuranceVerificationService.findFirstByPrescriptionIdAndCarrierTypeOrderByIdDesc(iv.getPrescriptionId(), Constants.PRIMARY);
        } else if (Constants.SENARY.equals(iv.getCarrierType())) {
            InsuranceVerification septenaryPlan = insuranceVerificationService.findFirstByPrescriptionIdAndCarrierTypeOrderByIdDesc(iv.getPrescriptionId(), Constants.SEPTENARY);
            otherPatientInsuranceVerification = septenaryPlan != null ? septenaryPlan : insuranceVerificationService.findFirstByPrescriptionIdAndCarrierTypeOrderByIdDesc(iv.getPrescriptionId(), Constants.PRIMARY);
        } else if (Constants.SEPTENARY.equals(iv.getCarrierType())) {
            InsuranceVerification octonaryPlan = insuranceVerificationService.findFirstByPrescriptionIdAndCarrierTypeOrderByIdDesc(iv.getPrescriptionId(), Constants.OCTONARY);
            otherPatientInsuranceVerification = octonaryPlan != null ? octonaryPlan : insuranceVerificationService.findFirstByPrescriptionIdAndCarrierTypeOrderByIdDesc(iv.getPrescriptionId(), Constants.PRIMARY);
        } else if (Constants.OCTONARY.equals(iv.getCarrierType())) {
            InsuranceVerification nonaryPlan = insuranceVerificationService.findFirstByPrescriptionIdAndCarrierTypeOrderByIdDesc(iv.getPrescriptionId(), Constants.NONARY);
            otherPatientInsuranceVerification = nonaryPlan != null ? nonaryPlan : insuranceVerificationService.findFirstByPrescriptionIdAndCarrierTypeOrderByIdDesc(iv.getPrescriptionId(), Constants.PRIMARY);
        } else if (Constants.NONARY.equals(iv.getCarrierType())) {
            InsuranceVerification denaryPlan = insuranceVerificationService.findFirstByPrescriptionIdAndCarrierTypeOrderByIdDesc(iv.getPrescriptionId(), Constants.DENARY);
            otherPatientInsuranceVerification = denaryPlan != null ? denaryPlan : insuranceVerificationService.findFirstByPrescriptionIdAndCarrierTypeOrderByIdDesc(iv.getPrescriptionId(), Constants.PRIMARY);
        } else if (Constants.DENARY.equals(iv.getCarrierType())) {
            otherPatientInsuranceVerification = insuranceVerificationService.findFirstByPrescriptionIdAndCarrierTypeOrderByIdDesc(iv.getPrescriptionId(), Constants.PRIMARY);
        }
        otherPatientInsurance = otherPatientInsuranceVerification != null ? otherPatientInsuranceVerification.getPatientInsurance() : null;
        return otherPatientInsurance;
    }

    public Claim save(Claim claim) {
        if (claim.getId() == null && claim.getCreatedById() == null) {
            claim.setCreatedById(userService != null && userService.getCurrentUser() != null ? userService.getCurrentUser().getId() : 1L);
        }
        claim.setUpdatedById(userService != null && userService.getCurrentUser() != null ? userService.getCurrentUser().getId() : 1L);



        Optional<Claim> claimOptional = claim.getId() != null ? claimRepository.findById(claim.getId()) : Optional.empty();
        Claim currentClaim = claimOptional.orElse(null);
        if ((currentClaim == null) || (claim.getNymblStatusId() != null && (currentClaim.getNymblStatusId() != claim.getNymblStatusId()))) {
            NymblStatusHistory nymblStatusHistory = new NymblStatusHistory();
            nymblStatusHistory.setClaimId(claim.getId());
            nymblStatusHistory.setPrescriptionId(claim.getPrescriptionId());
            nymblStatusHistory.setNymblStatusId(claim.getNymblStatusId());
            nymblStatusHistory.setType("claim");
            nymblStatusHistory.setUpdatedById(claim.getUpdatedById());
            nymblStatusHistoryService.save(nymblStatusHistory);
        }

        return super.save(claim);
    }

    public Map<String, Object> saveForVersion(Claim claim) {
        if (claim.getId() == null && claim.getCreatedById() == null) {
            claim.setCreatedById(userService != null && userService.getCurrentUser() != null ? userService.getCurrentUser().getId() : 1L);
        }
        Map<String, Object> respMap = new HashMap<>();
        String strLockingMessage = "This claim has been edited by another user.  Please reload/re-open to get the latest version and re-save with your changes.";
        ClaimInfo currentClaim = findOne(claim.getId());
        if(null != currentClaim && claim.getVersion().intValue() != currentClaim.getVersion().intValue()) {

            claim.setTotalClaimPaid(claim.getTotalClaimPaid().setScale(2, RoundingMode.UP));
            claim.setTotalClaimAmount(claim.getTotalClaimAmount().setScale(2, RoundingMode.UP));
            claim.setTotalClaimBalance(claim.getTotalClaimBalance().setScale(2, RoundingMode.UP));
            claim.setTotalPtResponsibilityAmount(claim.getTotalPtResponsibilityAmount().setScale(2, RoundingMode.UP));
            claim.setTotalPtResponsibilityBalance(claim.getTotalPtResponsibilityBalance().setScale(2, RoundingMode.UP));
            claim.setTotalPtResponsibilityPaid(claim.getTotalPtResponsibilityPaid().setScale(2, RoundingMode.UP));
            claim.setUncollected(claim.getUncollected().setScale(2, RoundingMode.UP));

            respMap = getAuditDetails(claim.getId(), optimisticLockingUtil.checkVersionLock(claim, currentClaim, strLockingMessage, Claim.class, currentClaim.getId()));
        } else {
            respMap.put(SAVED, save(claim));
            if (featureFlagService.findFeatureFlagByFeature("live_gl") != null) {
                try {
                    generalLedgerService2.insertUpdateSalesEntry(claim);
                } catch (Exception e) {
                    log.error(StringUtil.getExceptionAsString(e));
                }
            }
        }
        return respMap;
    }

    public Claim buildClaimForSearch() {
        Claim claim = new Claim();
        claim.setTotalClaimAmount(null);
        claim.setTotalClaimPaid(null);
        claim.setTotalClaimBalance(null);
        claim.setTotalPtResponsibilityAmount(null);
        claim.setTotalPtResponsibilityPaid(null);
        claim.setTotalPtResponsibilityBalance(null);

        return claim;
    }

    public Page<ClaimSearchDTO> sortSearch(Long claimId, Long patientId, String status, Long nymblStatusId, Long branchId
            , Long insuranceCompanyId, Long userId, java.sql.Date startDate, java.sql.Date endDate, java.sql.Date dosStartDate
            , java.sql.Date dosEndDate, Boolean showUnresolved, Pageable pageable, Boolean export, Long prescriptionId) {
        return populateClaimSearchDTOS(claimId, patientId, status, nymblStatusId, branchId, insuranceCompanyId, userId, startDate
                , endDate, dosStartDate, dosEndDate, showUnresolved, pageable, export, entityManager, prescriptionId);
    }

    public Page<ClaimSearchDTO> populateClaimSearchDTOS(Long claimId, Long patientId, String status, Long nymblStatusId
            , Long branchId, Long insuranceCompanyId, Long userId, java.sql.Date startDate, java.sql.Date endDate
            , java.sql.Date dosStartDate, java.sql.Date dosEndDate, Boolean showUnresolved, Pageable pageable
            , Boolean export, EntityManager entityManager, Long prescriptionId) {
        Map<String, String> sortPropertiesMap = getSortPropertiesMap();

        updateSortPropertiesMap(pageable, sortPropertiesMap);

        // Entity Manager used instead of repository method for paged data retrieval because repository queries don't support parameters for ordering
        String querySegment = constructQuerySegment(claimId, status, nymblStatusId, userId, insuranceCompanyId, patientId, branchId, startDate, endDate, dosStartDate, dosEndDate, showUnresolved, prescriptionId);
        List<Claim> claims = findClaims(querySegment, sortPropertiesMap.get("fieldName"), sortPropertiesMap.get("direction"), sortPropertiesMap.get("offset"), export, entityManager);
        String sql = "SELECT COUNT(c.id)";
        Long claimsLength = (Long) entityManager.createNativeQuery(sql.concat(querySegment)).getSingleResult();

        List<ClaimSearchDTO> dtoResults = new ArrayList<>();
        for (Claim claim : claims) {
            if (claim.getId() == 0) continue;
            ClaimSearchDTO dto = new ClaimSearchDTO();
            List<ClaimSubmission> submissions = claim.getClaimSubmissions();
            java.sql.Date appliedDate = appliedPaymentRepository.findLastAppliedDateByClaimId(claim.getId());
            Integer daysInClaimStatus = claimRepository.getDaysInClaimStatus(claim.getId());
            List<Task> tasks = claim.getTasks();
            List<Task> followUpTasks = tasks.stream().filter(t -> "follow_up".equals(t.getType())).collect(Collectors.toList());
            ClearingHousePayer clearingHousePayer = null;
            if (claim.getPatientInsurance() != null) {
                insuranceCompanyService.loadForeignKeys(claim.getPatientInsurance().getInsuranceCompany());
                clearingHousePayer = claim.getPatientInsurance().getInsuranceCompany().getClearingHousePayer();
                dto.setPayerId(clearingHousePayer != null ? clearingHousePayer.getPayerId() : "");
            }
            List<Object[]> salesNumbers = insuranceVerificationLCodeService.getProperSalesNumbersByPrescriptionId(claim.getPrescriptionId());
            loadForeignKeys(claim);
            dto.setClaim(claim);
            if (!CollectionUtils.isEmpty(submissions)) {
                ClaimSubmission cs = submissions.stream().sorted(Comparator.comparing(ClaimSubmission::getSubmissionDate,
                        Comparator.nullsLast(Comparator.naturalOrder())).reversed()).collect(Collectors.toList()).get(0);
                dto.setSubmissionType(cs.getClaimFileId() != null ? "Electronic" : "Paper");
                // the if is a temp fix for void testExportClaims() (Brett)
                if (cs.getPatientInsurance() != null && cs.getPatientInsurance().getInsuranceCompany() != null && cs.getPatientInsurance().getInsuranceCompany().getClearingHousePayerId() != null) {
                    insuranceCompanyService.loadForeignKeys(cs.getPatientInsurance().getInsuranceCompany());
                    ClearingHousePayer cp = cs.getPatientInsurance().getInsuranceCompany().getClearingHousePayer();
                    dto.setPayerId(cp != null ? cp.getPayerId() : "");
                }
            }
            dto.setClaimSubmissionDate(!submissions.isEmpty() ? submissions.get(submissions.size() - 1).getSubmissionDate() : null);
            dto.setClaimBillable(salesNumbers != null && !salesNumbers.isEmpty() ? (BigDecimal) salesNumbers.get(0)[2] : BigDecimal.ZERO);
            dto.setClaimAllowable(salesNumbers != null && !salesNumbers.isEmpty() ? (BigDecimal) salesNumbers.get(0)[3] : BigDecimal.ZERO);
            dto.setAppliedPaymentDate(appliedDate);
            dto.setDaysInClaimStatus(daysInClaimStatus);
            dto.setFollowUpTasks(!followUpTasks.isEmpty() ? followUpTasks : null);
            dtoResults.add(dto);
        }
        return new PageImpl<>(dtoResults, pageable, claimsLength.intValue());
    }

    public void updateSortPropertiesMap(Pageable pageable, Map<String, String> sortPropertiesMap) {
        if (pageable != null) {
            Sort sort = pageable.getSort();
            sortPropertiesMap.put("offset", Long.toString(pageable.getOffset()));
            sortPropertiesMap.put("fieldName", "c.id");
            sortPropertiesMap.put("direction", "DESC");
            List<String> sortProperties = Arrays.asList("c.id", "c.status", "c.nymbl_status_id", "c.total_claim_amount",
                    "p.id", "p.last_name", "b.name", "ic.name", "cs.submission_date", "ap.applied_date", "c.date_of_service", "n_user.last_name");
            for (String property : sortProperties) {
                if (sort.getOrderFor(property) != null) {
                    sortPropertiesMap.put("fieldName", sort.getOrderFor(property).getProperty());
                    sortPropertiesMap.put("direction", sort.getOrderFor(property).getDirection().isAscending() ? "ASC" : "DESC");
                    break;
                }
            }
        }
    }

    public Map<String, String> getSortPropertiesMap() {
        Map<String, String> sortPropertiesMap = new HashMap<>();
        sortPropertiesMap.put("offset", "0");
        sortPropertiesMap.put("fieldName", "c.id");
        sortPropertiesMap.put("direction", "DESC");
        return sortPropertiesMap;
    }

    public String constructQuerySegment(Long claimId, String status, Long nymblStatusId, Long userId
            , Long insuranceCompanyId, Long patientId, Long branchId, Date startDate, Date endDate
            , Date dosStartDate, Date dosEndDate, Boolean showUnresolved, Long prescriptionId) {
        boolean showFollowUps = startDate != null && endDate != null;
        boolean showDos = dosStartDate != null && dosEndDate != null;
        String dateStart = DateUtil.getStringDate(startDate, DF_YYYY_MM_DD_HH_MM);
        String dateEnd = DateUtil.getStringDate(DateUtil.getEndOfDay(endDate), DF_YYYY_MM_DD_HH_MM);
        String dosDateStart = DateUtil.getStringDate(dosStartDate, DF_YYYY_MM_DD_HH_MM);
        String dosDateEnd = DateUtil.getStringDate(DateUtil.getEndOfDay(dosEndDate), DF_YYYY_MM_DD_HH_MM);
        String baseSegment = " FROM claim c LEFT JOIN (SELECT claim_id, MAX(submission_date) AS submission_date FROM claim_submission GROUP BY claim_id) AS cs ON cs.claim_id = c.id "
                .concat("LEFT JOIN (SELECT claim_id, MAX(applied_date) AS applied_date FROM applied_payment GROUP BY claim_id) AS ap ON ap.claim_id = c.id ")
                .concat("LEFT JOIN prescription rx ON c.prescription_id =  rx.id ")
                .concat("LEFT JOIN patient p ON rx.patient_id = p.id ")
                .concat("LEFT JOIN patient_insurance pi ON c.responsible_patient_insurance_id = pi.id ")
                .concat("LEFT JOIN insurance_company ic ON pi.insurance_company_id = ic.id ")
                .concat("LEFT JOIN branch b ON c.billing_branch_id = b.id ")
                .concat("LEFT JOIN nymbl_master.user n_user ON c.user_id = n_user.id");
        baseSegment += showFollowUps ? " INNER JOIN task t ON c.id = t.claim_id AND t.type = 'follow_up'" : "";
        String nymblStatusSegment = " PLACEHOLDER c.nymbl_status_id = ".concat(nymblStatusId != null ? nymblStatusId.toString() : "");
        String userSegment = " PLACEHOLDER c.user_id = ".concat(userId != null ? userId.toString() : "");
        String insuranceCompanySegment = " PLACEHOLDER pi.insurance_company_id = ".concat(insuranceCompanyId != null ? insuranceCompanyId.toString() : "");
        String patientSegment = " PLACEHOLDER p.id = ".concat(patientId != null ? patientId.toString() : "");
        String branchSegment = " PLACEHOLDER c.billing_branch_id =".concat(branchId != null ? branchId.toString() : "");
        String followUpSegment = " PLACEHOLDER t.due_date BETWEEN ".concat(startDate != null && endDate != null ? ("'" + dateStart).concat("' AND '").concat(dateEnd + "'") : "");
        String dosSegment = " PLACEHOLDER c.date_of_service BETWEEN ".concat(dosStartDate != null && dosEndDate != null ? ("'" + dosDateStart).concat("' AND '").concat(dosDateEnd + "'") : "");
        String showUnresolvedSegment = " PLACEHOLDER c.date_resolved IS NULL";
        String claimIdSegment = " PLACEHOLDER c.id =".concat(claimId != null ? claimId.toString() : "");
        String prescriptionIdSegment = " PLACEHOLDER rx.id =".concat(prescriptionId != null ? prescriptionId.toString() : "");

        String replaceableQuery = baseSegment +
                (nymblStatusId != null ? nymblStatusSegment : "") +
                (userId != null ? userSegment : "") +
                (insuranceCompanyId != null ? insuranceCompanySegment : "") +
                (patientId != null ? patientSegment : "") +
                (branchId != null ? branchSegment : "") +
                (claimId != null ? claimIdSegment : "") +
                (prescriptionId != null ? prescriptionIdSegment : "") +
                (showFollowUps ? followUpSegment : "") +
                (showDos ? dosSegment : "") +
                (showUnresolved ? showUnresolvedSegment : "");

        return replaceableQuery.replaceFirst("PLACEHOLDER", "WHERE").replaceAll("PLACEHOLDER", "AND");
    }

    @SuppressWarnings(value = "unchecked")
    public List<Claim> findClaims(String querySegment, String fieldName, String direction, String offset, Boolean export, EntityManager entityManager) {
        String query = populateFindClaimsQuery(querySegment, fieldName, direction, offset, export);
        List<Claim> claims = getClaimsByEntityManager(query, entityManager);
        loadForeignKeysForClaimResults(claims);
        return claims;
    }

    public String populateFindClaimsQuery(String querySegment, String fieldName, String direction, String offset, Boolean export) {
        int len = 30;
        if (export != null && export) {
            len = 999999;
        }
        return "SELECT c.*".concat(querySegment).concat(" ORDER BY ".concat(fieldName).concat(" ").concat(direction).concat(" LIMIT ").concat(offset).concat(", ").concat(Integer.toString(len)));
    }

    @SuppressWarnings("unchecked")
    public List<Claim> getClaimsByEntityManager(String query, EntityManager entityManager) {
        //System.out.println("getClaimsByEntityManager = "+query);
        return entityManager.createNativeQuery(query, Claim.class).getResultList();
    }

    @Timed
    public String exportClaims(Long claimId, Long patientId, String status, Long nymblStatusId, Long branchId
            , Long insuranceCompanyId, Long userId, java.sql.Date sqlStartDate, java.sql.Date sqlEndDate
            , java.sql.Date sqlDosStartDate, java.sql.Date sqlDosEndDate, Boolean showUnresolved
            , Pageable pageable, Boolean export, Long prescriptionId) {
        Page<ClaimSearchDTO> claimDTOs = sortSearch(claimId, patientId, status, nymblStatusId, branchId
                , insuranceCompanyId, userId, sqlStartDate, sqlEndDate, sqlDosStartDate, sqlDosEndDate
                , showUnresolved, pageable, export, prescriptionId);
        return exportClaimsFromClaimDTOs(claimDTOs);
    }

    public String exportClaimsFromClaimDTOs(Page<ClaimSearchDTO> claimDTOs) {
        StringBuilder sb = new StringBuilder();
        sb.append("Patient Id,Claim Id,Prescription Id,Patient Id,Patient First Name,Patient Last Name,Date of Service,Status,Patient Branch,Billing Branch,RX Branch,Total Billable,Total Allowable,Total Insurance Amount,Total Insurance Balance,Total Insurance Payments,Total Patient Responsibility Amount,Total Patient Responsibility Balance,Total Patient Responsibility Paid,User Id,Patient Insurance,Claim Submission Date,Submission Aging (days-type),Applied Payment Date\n");

        SystemSetting displayBillerCodeChecked = systemSettingService.findBySectionAndField("billing", "display_biller_code_field");

        try {
            for (ClaimSearchDTO claimDTO : claimDTOs.getContent()) {
                Claim claim = claimDTO.getClaim();
                //TODO: BRM
                sb.append(claim.getPatientInsurance() != null && claim.getPatientInsurance().getPatient() != null ? claim.getPatientInsurance().getPatient().getId().toString().concat(",") : ",");
                sb.append(claim.getId() != null ? claim.getId().toString().concat(",") : ",");
                sb.append(claim.getPrescriptionId() != null ? claim.getPrescriptionId().toString().concat(",") : ",");
                if(claim.getPrescription() != null && claim.getPrescription().getPatient() != null){
                    sb.append(claim.getPrescription().getPatientId() != null ? claim.getPrescription().getPatientId().toString().concat(",\"") : ",\"");
                    sb.append(claim.getPrescription().getPatient().getFirstName().concat("\",\""));
                    sb.append(claim.getPrescription().getPatient().getLastName().concat("\","));
                } else{
                    sb.append(",");
                    sb.append(",");
                    sb.append(",");
                }
                sb.append(claim.getDateOfService() != null ? claim.getDateOfService().toString().concat(",") : ",");
                // sb.append(claim.getStatus() != null ? claim.getStatus().concat(",") : ",");
                sb.append(claim.getNymblStatus() != null && claim.getNymblStatus().getName() != null ? claim.getNymblStatus().getName().concat(",") : ",");
                sb.append(claim.getPrescription().getPatient().getPrimaryBranch() != null && claim.getPrescription().getPatient().getPrimaryBranch().getName() != null ? claim.getPrescription().getPatient().getPrimaryBranch().getName().concat(",") : ",");
                sb.append(claim.getBillingBranch() != null && claim.getBillingBranch().getName() != null ? claim.getBillingBranch().getName().concat(",") : ",");
                sb.append(claim.getPrescription().getBranch() != null && claim.getPrescription().getBranch().getName() != null ? claim.getPrescription().getBranch().getName().concat(",") : ",");
                sb.append(claimDTO.getClaimBillable() != null ? claimDTO.getClaimBillable().toPlainString().concat(",") : ",");
                sb.append(claimDTO.getClaimAllowable() != null ? claimDTO.getClaimAllowable().toPlainString().concat(",") : ",");
                sb.append(claim.getTotalClaimAmount() != null ? claim.getTotalClaimAmount().toPlainString().concat(",") : ",");
                sb.append(claim.getTotalClaimBalance() != null ? claim.getTotalClaimBalance().toPlainString().concat(",") : ",");
                sb.append(claim.getTotalClaimPaid() != null ? claim.getTotalClaimPaid().toPlainString().concat(",") : ",");
                sb.append(claim.getTotalPtResponsibilityAmount() != null ? claim.getTotalPtResponsibilityAmount().toPlainString().concat(",") : ",");
                sb.append(claim.getTotalPtResponsibilityBalance() != null ? claim.getTotalPtResponsibilityBalance().toPlainString().concat(",") : ",");
                sb.append(claim.getTotalPtResponsibilityPaid() != null ? claim.getTotalPtResponsibilityPaid().toPlainString().concat(",") : ",");
                sb.append(claim.getUserId() != null ? claim.getUserId().toString().concat(",") : ",");
                if(claim.getPatientInsuranceId() != null && claim.getPatientInsurance().getInsuranceCompany() != null && claim.getPatientInsurance().getInsuranceCompany().getName() != null){
                    String temp = StringUtils.isNotBlank(claimDTO.getPayerId()) ? " - ".concat(claimDTO.getPayerId()) : "";
                    if(displayBillerCodeChecked.getValue().equals("Y") && claim.getPatientInsurance().getInsuranceCompany().getBillerCode() != null) {
                        sb.append("\"").append(claim.getPatientInsurance().getInsuranceCompany().getName()).append(temp).append(" (").append(claim.getPatientInsurance().getInsuranceCompany().getBillerCode()).append(")\",");
                    } else {
                        sb.append("\"").append(claim.getPatientInsurance().getInsuranceCompany().getName()).append(temp).append("\",");
                    }
                } else {
                    sb.append(",");
                }
                sb.append(claimDTO.getClaimSubmissionDate() != null ? claimDTO.getClaimSubmissionDate().toString().concat(",") : ",");
                sb.append(claimDTO.getClaimSubmissionDate() != null ? DateUtil.getAgeInDaysFromDate(claimDTO.getClaimSubmissionDate()) + "-" + claimDTO.getSubmissionType().charAt(0) + "," : ",");
                sb.append(claimDTO.getAppliedPaymentDate() != null ? claimDTO.getAppliedPaymentDate().toString().concat("\n") : "\n");
            }
        } catch (Exception e) {
            log.error(StringUtil.getExceptionAsString(e));
        }
        return sb.toString();
    }

    public List<Claim> search(Long patientId, String status, Long nymblStatusId, Long branchId, Long insuranceCompanyId, Long userId, Date startDate, Date endDate, Pageable pageable) {
        List<Claim> results = null;
        Claim claim = buildClaimForSearch();
        Example<Claim> example = getClaimExample(patientId, status, nymblStatusId, branchId, insuranceCompanyId, userId, claim);

        if (example != null) {
            results = findAll(example, pageable);
        } else {
            results = findAll(pageable);
        }
        if (startDate != null && endDate != null) {
            List<Claim> claimsWithFollowUp = new ArrayList<>();
            for (Claim c : results) {
                boolean inDateRange = false;
                List<Task> followUpTasks = taskService.findByClaimIdAndType(c.getId(), "follow_up");
                if (followUpTasks.size() > 1) {
                    for (Task t : followUpTasks) {
                        if (!(t.getDueDate().before(startDate) || t.getDueDate().after(endDate))) {
                            inDateRange = true;
                            break;
                        }
                    }
                }
                if (inDateRange) {
                    claimsWithFollowUp.add(c);
                }
            }
            results = claimsWithFollowUp;
        }
        loadForeignKeysForClaimResults(results);
        return results;
    }

    public Example<Claim> getClaimExample(Long patientId, String status, Long nymblStatusId, Long branchId, Long insuranceCompanyId, Long userId, Claim claim) {
        Example<Claim> example = null;
        ExampleMatcher matcher = ExampleMatcher.matching();
        if (patientId != null || !StringUtil.isBlank(status) || nymblStatusId != null || branchId != null || insuranceCompanyId != null || userId != null) {
            Prescription p = new Prescription();
            PatientInsurance pi = new PatientInsurance();
//            claim.setStatus(status);
            claim.setNymblStatusId(nymblStatusId);
            p.setPatient(new Patient());
            p.setPatientInsurance(pi);
            if (patientId != null) {
                claim.setPrescription(p);
                claim.getPrescription().setPatientId(patientId);
                matcher = matcher.withMatcher("patientId", exact());
            }
            if (!StringUtil.isBlank(status)) {
                matcher = matcher.withMatcher("status", exact());
            }
            if (nymblStatusId != null) {
                matcher = matcher.withMatcher("nymblStatusId", exact());
            }
            if (branchId != null) {
                claim.setPrescription(p);
                claim.getPrescription().getPatient().setPrimaryBranchId(branchId);
                matcher = matcher.withMatcher("primaryBranchId", exact());
            }
            if (insuranceCompanyId != null) {
                claim.setPrescription(p);
                claim.getPrescription().getPatientInsurance().setInsuranceCompanyId(insuranceCompanyId);
                matcher = matcher.withMatcher("insuranceCompanyId", exact());
            }
            if (userId != null) {
                claim.setUserId(userId);
                matcher = matcher.withMatcher("userId", exact());
            }
            example = Example.of(claim, matcher);
        }
        return example;
    }

    public boolean sendClaimFiles(List<Long> claimIds, Long billingBranchId) throws X12Exception {
        boolean success = true;
        SystemSetting autoClaimFollowUpChecked = systemSettingService.findBySectionAndField("billing", "auto_claim_follow_up");
        SystemSetting format837 = systemSettingService.findBySectionAndField("claim", "format_837");
        ZonedDateTime today = ZonedDateTime.ofInstant(Instant.now(), ZoneId.of(TenantContext.getUserTimezoneId()));
        for (Long claimId : claimIds) {
            Claim claim = findOne(claimId);
            PatientInsurance patientInsurance = claim.getResponsiblePatientInsurance();
            if (patientInsurance != null) {
                Branch branch;
                if (billingBranchId != null) {
                    branch = branchService.findOne(billingBranchId);
                } else if (claim.getBillingBranch() != null) {
                    branch = claim.getBillingBranch();
                } else if (claim.getPrescription().getBranch() != null) {
                    branch = claim.getPrescription().getBranch();
                } else if (claim.getPrescription().getPatient().getPrimaryBranch() != null) {
                    branch = claim.getPrescription().getPatient().getPrimaryBranch();
                } else {
                    branch = userService.getCurrentBranchUnsafeDoNotUse(); // should never hit
                }
                String timestamp = DateUtil.getStringDate(new Date(), Constants.DF_YYYYMMDDHHmmssSSS);
                ClaimFile cf = null;
                if ("zirmed".equals(format837.getValue())) {
                    List<String> requests837 = create837Requests(claim, branch, patientInsurance.getId(), claim.getOtherPatientInsuranceId());
                    String claimData = StringUtils.join(requests837, "\n");
                    cf = createClaimFile(claim, branch.getId(), claimData, timestamp);
                    createClaimSubmission(claim.getId(), patientInsurance.getId(), cf);
                    claim.setNymblStatusId(21L);
                    save(claim);
                } else {
                    Factory837Parameters parameters = factory837.build(claim.getId(), timestamp, null, null, null, null);
                    if (parameters.getValidationErrors().size() > 0) {
                        throw new X12Exception(parameters.getValidationErrors());
                    }
                    String _837 = parameters.getX12Claim().toX12String();
                    if (BooleanUtils.toBooleanDefaultIfNull(branch.getUseRealTimeRulesEngine(), false) && claimIds.size() == 1 && !BooleanUtils.toBooleanDefaultIfNull(claim.getOverrideRTRulesEngine(), false)) {
                        Map<String, String> response = waystarAPI.realTimeRulesEngineRun(branch.getOutClearingHouse().getRestUser(), branch.getOutClearingHouse().getRestPassword(), _837);
                        if (response.get("Error") == null &&
                                response.get("Claim").equals(claimId.toString()) &&
                                response.get("Status").equals("Accept")) {
                            claim.setNymblStatusId(21L);
                            save(claim);
                            cf = createClaimFile(claim, branch.getId(), _837, timestamp);
                            createClaimSubmission(claim.getId(), patientInsurance.getId(), cf);
                        } else if (response.get("ErrorMessages") != null &&
                                response.get("Claim").equals(claimId.toString()) &&
                                response.get("Status").equals("Reject")) {
                            success = false;
                            String message = "Claim " + claimId + " was Rejected for the following: <br>" + response.get("ErrorMessages");
                            notificationService.convertAndSendUserNotification(notificationService.createClaimMessage(claimId, message));
                        } else {
                            success = false;
                            String message = "Claim " + claimId + " has failed Real-time Edit Rules on Waystar.";
                            notificationService.convertAndSendUserNotification(notificationService.createClaimMessage(claimId, message));
                        }
                    } else {
                        claim.setNymblStatusId(21L);
                        claim = save(claim);
                        cf = createClaimFile(claim, branch.getId(), _837, timestamp);
                        createClaimSubmission(claim.getId(), patientInsurance.getId(), cf);
                    }
                    if(BooleanUtils.toBooleanDefaultIfNull(parameters.getForm1500Template().getUseCapitatedPayerSpecialEdits(), false)){
                        if (cf != null) {
                            cf.setSubmitted(true);
                            cf = claimFileService.save(cf);
                        }
                        String _835 = parameters.getClaimPayment().toX12String();
                        String _835FileName = String.format("%s.%s.ERA.835",
                                parameters.getPatientInsuranceCompany().getClearingHousePayer().getPayerId(),
                                timestamp);
                        X12_File x12File = new X12_File();
                        x12File.setProcessed(false);
                        x12File.setType("835");
                        x12File.setContents(_835);
                        x12File.setFilename(_835FileName);
                        x12FileService.save(x12File);
                        NymblStatus ns = nymblStatusService.findByKey("awaiting_payment");
                        claim.setNymblStatusId(ns.getId());
                        save(claim);
                    }
                }

                if (autoClaimFollowUpChecked.getValue().equals("Y") && patientInsurance.getInsuranceCompany().getFollowUpDays() != null && patientInsurance.getInsuranceCompany().getFollowUpMessage() != null) {
                    Task followUpTask = populateFollowUpTask(today, claim, patientInsurance);
                    TableObjectContainer.storeForJUnitTestingIfApplicationIsNotRunning(followUpTask);
                    taskService.save(followUpTask);
                    notificationService.createTaskNotification(followUpTask);
                }
            } else {
//                claim.setStatus("post_to_pt_responsibility");
                claim.setNymblStatusId(19L);
                save(claim);
                createClaimSubmission(claim.getId(), null, null);
            }
            if (success) {
                if (featureFlagService.findFeatureFlagByFeature("live_gl") != null) {
                    try {
                        generalLedgerService2.insertUpdateSalesEntry(claim);
                    } catch (Exception e) {
                        log.error(StringUtil.getExceptionAsString(e));
                    }
                }
            }
        }
        return success;
    }

    public Task populateFollowUpTask(ZonedDateTime today, Claim claim, PatientInsurance patientInsurance) {
//        DateTime date = today.plusDays(patientInsurance.getInsuranceCompany().getFollowUpDays().intValue());
//        Date followUpDate = date.toDate();
//        java.sql.Date sqlFollowUpDate = new java.sql.Date(followUpDate.getTime());
        ZonedDateTime date = today.plusDays(patientInsurance.getInsuranceCompany().getFollowUpDays().intValue());
        java.sql.Date sqlFollowUpDate = java.sql.Date.valueOf(date.toLocalDate());
        Task task = new Task();
        task.setName("Follow Up for Claim #" + claim.getId());
        task.setDescription(patientInsurance.getInsuranceCompany().getFollowUpMessage());
        task.setPatientId(claim.getPrescription().getPatient().getId());
        task.setPrescriptionId(claim.getPrescriptionId());
        task.setClaimId(claim.getId());
        task.setUserId(claim.getUserId());
        // Load the user to avoid NullPointerException in notification service
        if (claim.getUserId() != null) {
            task.setUser(userService.findOne(claim.getUserId()));
        }
        task.setCompleted(false);
        task.setCreatedAt(new Timestamp(System.currentTimeMillis()));
        task.setUpdatedAt(new Timestamp(System.currentTimeMillis()));
        task.setDueDate(sqlFollowUpDate);
        Long currentUserId = userService.getCurrentUser().getId();
        task.setCreatedById(currentUserId);
        task.setType("follow_up");
        return task;
    }

    public ClaimFile createClaimFile(Claim claim, Long branchId, String contents, String timestamp) {
        ClaimFile cf = new ClaimFile();
        String filename = claim.getId().toString().concat("_").concat(timestamp).concat("_").concat(claim.getResponsiblePatientInsurance().getInsuranceCompanyId().toString()).concat(".CLP");
        cf.setFilename(filename);
        cf.setSubmitted(false);
        cf.setClaimId(claim.getId());
        cf.setContents(contents);
        cf.setBranchId(branchId);
        cf = claimFileService.save(cf);
        cf = claimFileService.findOne(cf.getId());
        return cf;
    }

    public void createClaimSubmission(Long claimId, Long patientInsuranceId, ClaimFile claimFile) {
        ClaimSubmission submission = new ClaimSubmission();
        submission.setClaimId(claimId);
        submission.setPatientInsuranceId(patientInsuranceId);
        submission.setStatus("");
        submission.setSubmissionDate(java.sql.Date.valueOf(ZonedDateTime.now(ZoneId.of(TenantContext.getUserTimezoneId())).toLocalDate()));
        submission.setSubmittedById(userService.getCurrentUser().getId());
        //the prevents: Cannot add or update a child row: a foreign key constraint fails (`claim_submission`, CONSTRAINT `FK_claim_submission_claimfileid` FOREIGN KEY (`claim_file_id`) REFERENCES `claim_file` (`id`))
        submission = claimSubmissionService.save(submission);
        if (claimFile != null) {
            submission.setClaimFileId(claimFile.getId());
            claimSubmissionService.save(submission);
        }
        if (featureFlagService.findFeatureFlagByFeature("live_gl") != null) {
            try {
                generalLedgerService2.insertUpdateSalesEntry(claimFile.getClaim());
            } catch (Exception e) {
                log.error(StringUtil.getExceptionAsString(e));
            }
        }
        notificationService.reloadClaimSubmissionNotification("reloadClaimSubmissions", claimId);
    }

    public String getTotalAmountPaid(Claim claim) {
        InsuranceVerification iv = insuranceVerificationService.findByPatientInsuranceIdAndPrescriptionId(claim.getPatientInsuranceId(), claim.getPrescriptionId());
        if (Constants.PRIMARY.equals(iv.getCarrierType())) {
            return claim.getTotalClaimPaid().equals(BigDecimal.ZERO) ? "" : new DecimalFormat(Constants.DF0).format(claim.getTotalClaimPaid());
        }
        List<Claim> claims = findByPrescriptionId(claim.getPrescriptionId());
        claims.removeIf((Claim c) -> c.getPatientInsuranceId().equals(claim.getPatientInsuranceId())); //Remove the current claim so it doesn't get included in summation below

        BigDecimal total = BigDecimal.ZERO;
        for (Claim c : claims) {
            total = total.add(c.getTotalClaimPaid());
        }
        return total.equals(BigDecimal.ZERO) ? "" : new DecimalFormat(Constants.DF0).format(total);
    }

    @Deprecated
    public List<String> create837Requests(Claim claim, Branch branch, Long patientInsuranceId, Long otherPatientInsuranceId) {
        // for some reason, sometimes patientInsuranceId is null
        if (patientInsuranceId == null || patientInsuranceId <= 0)
            patientInsuranceId = claim.getResponsiblePatientInsuranceId();

        List<String> requests = new ArrayList<>();

        Map<String, String> claimDefaults = new HashMap<>();
        List<SystemSetting> list = systemSettingService.findBySection("claim");
        for (SystemSetting ss : list) {
            claimDefaults.put(ss.getField(), ss.getValue());
        }
        Company company = userService.getCurrentCompany();
        Prescription prescription = claim.getPrescription();
        prescriptionService.loadForeignKeys(prescription); // Needed to retrieve treating practitioner info.
        PatientInsurance patientInsurance = patientInsuranceService.findOne(patientInsuranceId);
        Patient patient = prescription.getPatient();

        // May need to change to only find by prescription id only because errors caused when finding by patientInsuranceId as well
        InsuranceVerification insuranceVerification = insuranceVerificationService.findByPatientInsuranceIdAndPrescriptionId(patientInsuranceId, prescription.getId());
//        List<InsuranceVerification_L_Code> lCodes = insuranceVerificationLCodeService.findByInsuranceVerificationIdAndCoveredTrueAndOrderByPlcOrderNum(insuranceVerification.getId());
        List<InsuranceVerification_L_Code> lCodes = insuranceVerificationLCodeService.findByInsuranceVerificationIdAndBillTrueAndOrderByPlcOrderNum(insuranceVerification.getId());

        String[] dataFields = new String[Waystar837P.Note_12.ordinal() + 1];
        for (int j = 0; j <= 293; j++) {
            dataFields[j] = "";
        }
        loadSubscriberFields(prescription.getPatientId(), prescription.getId(), patientInsuranceId, claim.getId(), patientInsurance, dataFields);
        loadOtherSubscriberFields(otherPatientInsuranceId, dataFields);
        dataFields[Waystar837P.PatLName.ordinal()] = patient.getLastName();
        dataFields[Waystar837P.PatFName.ordinal()] = patient.getFirstName();
        dataFields[Waystar837P.PatMName.ordinal()] = patient.getMiddleName();
        dataFields[Waystar837P.PatBirthdate.ordinal()] = DateUtil.getStringDate(patient.getDob(), Constants.WAYSTAR_DATE_FORMAT);

        // SCRUM-3553 ********* This appears to be correct
        dataFields[Waystar837P.PatSex.ordinal()] = patient.getGender() != null ? StringUtil.getGender(patient.getGender()) : "";
        dataFields[Waystar837P.PatAddr.ordinal()] = patient.getStreetAddress();
        dataFields[Waystar837P.PatCity.ordinal()] = patient.getCity();
        dataFields[Waystar837P.PatState.ordinal()] = patient.getState();
        dataFields[Waystar837P.PatZip.ordinal()] = patient.getZipcode();
        dataFields[Waystar837P.PatPhone.ordinal()] = StringUtil.formatPhone(patient.getHomePhone());
        dataFields[Waystar837P.PatRel.ordinal()] = StringUtil.relationToSubscriber(patientInsurance.getRelationToSubscriber());
        dataFields[Waystar837P.EmpRelatedYes.ordinal()] = prescription.getAccidentDate() == null ? "N" : "E".equals(prescription.getAccidentType()) ? "Y" : "N";
        dataFields[Waystar837P.AutoAccidYes.ordinal()] = prescription.getAccidentDate() == null ? "N" : "A".equals(prescription.getAccidentType()) ? "Y" : "N";
        dataFields[Waystar837P.AutoAccidState.ordinal()] = prescription.getAccidentState() == null ? "" : prescription.getAccidentState();
        dataFields[Waystar837P.OtherAccidYes.ordinal()] = prescription.getAccidentDate() == null ? "N" : "O".equals(prescription.getAccidentType()) ? "Y" : "N";
        dataFields[Waystar837P.IllnessDate.ordinal()] = prescription.getAccidentDate() == null ? "" : DateUtil.getStringDate(prescription.getAccidentDate(), Constants.WAYSTAR_DATE_FORMAT);
        dataFields[Waystar837P.IllnessDateQual.ordinal()] = prescription.getAccidentDate() == null ? "" : "431";
        dataFields[Waystar837P.SbrPolicyGroup.ordinal()] = Strings.isNullOrEmpty(patientInsurance.getGroupNumber()) ? "NONE" : patientInsurance.getGroupNumber();
        dataFields[Waystar837P.SbrBirthdate.ordinal()] = DateUtil.getStringDate(patientInsurance.getDob(), Constants.WAYSTAR_DATE_FORMAT);


        // SCRUM-3553 ********* This appears to be correct
        dataFields[Waystar837P.SbrSex.ordinal()] = patientInsurance.getGender() != null ? StringUtil.getGender(patientInsurance.getGender()) : "";
        dataFields[Waystar837P.InsGroupName.ordinal()] = patientInsurance.getInsuranceCompany().getName();
        dataFields[Waystar837P.PatSignature.ordinal()] = "SOF";
        dataFields[Waystar837P.PatSignatureDate.ordinal()] = DateUtil.getStringDate(prescription.getDeliveredOn() != null ? prescription.getDeliveredOn() : prescription.getManualPodSignedDate(), Constants.WAYSTAR_DATE_FORMAT);
        dataFields[Waystar837P.SbrSignature.ordinal()] = "SOF";

        InsuranceCompany insuranceCompany = claim.getResponsiblePatientInsurance().getInsuranceCompany();
        String physicianToUse = insuranceCompany.getPhysicianToUse();
        Physician physician = null;
        if ("referring_physician".equals(physicianToUse)) {
            physician = prescription.getReferringPhysician();
        } else if ("primary_care_physician".equals(physicianToUse)) {
            physician = prescription.getPrimaryCarePhysician();
        }
        dataFields[Waystar837P.RefProvLName.ordinal()] = physician != null ? physician.getLastName() : "";
        dataFields[Waystar837P.RefProvFName.ordinal()] = physician != null ? physician.getFirstName() : "";
        dataFields[Waystar837P.RefProvMName.ordinal()] = physician != null ? physician.getMiddleName() : "";
        dataFields[Waystar837P.RefProvSuffix.ordinal()] = physician != null ? physician.getCredentials() : "";
        dataFields[Waystar837P.RefProvQual.ordinal()] = insuranceCompany.getPhysicianQualifier();
        dataFields[Waystar837P.RefNPI.ordinal()] = physician != null ? physician.getNpi() : "";
        dataFields[Waystar837P.AdmissionFrom.ordinal()] = "";
        dataFields[Waystar837P.AdditionalClaimInfo.ordinal()] = claim.getAdditionalInfo();
        dataFields[Waystar837P.ResubCode.ordinal()] = claim.getResubmissionCode();
        dataFields[Waystar837P.OriginalRefNum.ordinal()] = claim.getOriginalRefNum();

        List<PrescriptionDiagnosisCode> prescriptionDiagnosisCodes = prescriptionDiagnosisCodeService.findByPrescriptionId(prescription.getId());
        int index = Waystar837P.DiagCodeA.ordinal();
        for (PrescriptionDiagnosisCode pdc : prescriptionDiagnosisCodes) {
            dataFields[index] = pdc.getDiagnosisCode().getCode();
            index++;
        }

        dataFields[Waystar837P.ICDindicator.ordinal()] = "0";
        dataFields[Waystar837P.PriorAuth.ordinal()] = insuranceVerification.getReferralNumber();

        Boolean useBranchTaxId = company.getUseBranchTaxId();
        String taxId = claimDefaults.get("tax_id_number");
        String taxIdType = claimDefaults.get("tax_id_to_use");
        if (useBranchTaxId != null && useBranchTaxId) {
            taxId = branch.getTaxId();
            taxIdType = branch.getTaxIdType();
        }

        dataFields[Waystar837P.BillTin.ordinal()] = taxId;
        dataFields[Waystar837P.BillTinType.ordinal()] = taxIdType.equals("ssn") ? "S" : taxIdType.equals("ein") ? "E" : "";

        dataFields[Waystar837P.ClaimRef.ordinal()] = "P".concat(patient.getId().toString()).concat("C").concat(claim.getId().toString());
        dataFields[Waystar837P.AcceptAssignmentYesNo.ordinal()] = claim.getAcceptAssignment() != null && claim.getAcceptAssignment() ? "Y" : "N";

        //        String otherId = renderingProviderOtherId(claimDefaults.g et("rendering_provider_other_id"), claim);

        User tp = userService.getUserById(prescription.getTreatingPractitionerId());
        FullName name = (tp != null) ? new FullName(tp.getFirstName(), tp.getMiddleName(), tp.getLastName(), tp.getCredentials()) : new FullName();
        String facilityName = Boolean.TRUE.equals(branch.getUseBranchName()) ? branch.getName() : company.getName();
        String providerInformation = insuranceVerification.getPatientInsurance().getInsuranceCompany().getForm1500Template().getBox31ProviderInformation().name();
        if (providerInformation.equalsIgnoreCase("facility_information")) {
            dataFields[Waystar837P.RenLName.ordinal()] = facilityName;
        } else if (providerInformation.equalsIgnoreCase("signature_on_file")) {
            dataFields[Waystar837P.RenLName.ordinal()] = "Signature on File";
        } else {
            dataFields[Waystar837P.RenLName.ordinal()] = StringUtil.formatName(name, "FMiLC", true);
        }

        //dataFields[Zirmed837P.RenLName.ordinal()] = claimDefaults.get("provider_information").equals("facility_information") ? facilityName : StringUtil.formatName(name, "FMiLC", true);
        dataFields[Waystar837P.RenProvSignatureDate.ordinal()] = DateUtil.getStringDate(DateUtil.getCurrentDate(), Constants.WAYSTAR_DATE_FORMAT);

        dataFields[Waystar837P.FacilityProvNum.ordinal()] = claimDefaults.get("facility_taxonomy");

        populateZirmedFacilityDataFields(branch, !StringUtil.isBlank(branch.getTagLine()) ? branch.getTagLine() : facilityName, claimDefaults, prescription, dataFields);

        dataFields[Waystar837P.BillPhone.ordinal()] = StringUtil.formatPhone(branch.getBillingPhoneNumber());
        dataFields[Waystar837P.BillName.ordinal()] = branch.getBillingCompanyName();
        dataFields[Waystar837P.BillAddr1.ordinal()] = branch.getBillingStreetAddress();
        dataFields[Waystar837P.BillCity.ordinal()] = branch.getBillingCity();
        dataFields[Waystar837P.BillState.ordinal()] = branch.getBillingState();
        dataFields[Waystar837P.BillZip.ordinal()] = branch.getBillingZipcode();
        dataFields[Waystar837P.BillNPI.ordinal()] = branch.getBillingNpi();
        dataFields[Waystar837P.BillProvNum.ordinal()] = claimDefaults.get("billing_taxonomy");
        dataFields[Waystar837P.BillProvNumQual.ordinal()] = StringUtil.isBlank(claimDefaults.get("billing_taxonomy")) ? "" : "ZZ";
        dataFields[Waystar837P.PayerName.ordinal()] = getPayerName(patientInsurance, claimDefaults.get("use_icb_name_for_hcfa"));
        dataFields[Waystar837P.PayerAdd1.ordinal()] = patientInsurance.getInsuranceCompanyBranch() == null ? null : patientInsurance.getInsuranceCompanyBranch().getStreetAddress();
        dataFields[Waystar837P.PayerCity.ordinal()] = patientInsurance.getInsuranceCompanyBranch() == null ? null : patientInsurance.getInsuranceCompanyBranch().getCity();
        dataFields[Waystar837P.PayerState.ordinal()] = patientInsurance.getInsuranceCompanyBranch() == null ? null : patientInsurance.getInsuranceCompanyBranch().getState();
        dataFields[Waystar837P.PayerZip.ordinal()] = patientInsurance.getInsuranceCompanyBranch() == null ? null : patientInsurance.getInsuranceCompanyBranch().getZipcode();

        populateDataFieldsForInsuranceVerificationLCodes(claim, branch, claimDefaults.get("rendering_provider_other_id"), requests, lCodes, dataFields);
        return requests;
    }

    public void populateDataFieldsForInsuranceVerificationLCodes(Claim claim, Branch branch, String renderingProviderOtherId, List<String> requests, List<InsuranceVerification_L_Code> lCodes, String[] dataFields) {
        int dos_index = Waystar837P.ServiceFrom_1.ordinal(),
                svcto_index = Waystar837P.ServiceTo_1.ordinal(),
                pos_index = Waystar837P.POS_1.ordinal(),
                emg_index = Waystar837P.Emg_1.ordinal(),
                l_code_index = Waystar837P.ProcCode_1.ordinal(),
                modifiers_index = Waystar837P.Modifiers_1.ordinal(),
                diagnosis_pointer_index = Waystar837P.DiagPtrs_1.ordinal(),
                charge_index = Waystar837P.Charge_1.ordinal(),
                unit_index = Waystar837P.Units_1.ordinal(),
                epsdt_index = Waystar837P.EPSDT_1.ordinal(),
                note_index = Waystar837P.Note_1.ordinal(),
                rendering_provider_npi_index = Waystar837P.rennpi_1.ordinal(),
                l_code_auth_qualifier_index = Waystar837P.renprovnumqual_1.ordinal(),
                l_code_auth_number_index = Waystar837P.renprovnum_1.ordinal();
        int checker = 0;
        for (InsuranceVerification_L_Code lCode : lCodes) {
            if (checker == 12) {
                dos_index = Waystar837P.ServiceFrom_1.ordinal();
                svcto_index = Waystar837P.ServiceTo_1.ordinal();
                pos_index = Waystar837P.POS_1.ordinal();
                emg_index = Waystar837P.Emg_1.ordinal();
                l_code_index = Waystar837P.ProcCode_1.ordinal();
                modifiers_index = Waystar837P.Modifiers_1.ordinal();
                diagnosis_pointer_index = Waystar837P.DiagPtrs_1.ordinal();
                charge_index = Waystar837P.Charge_1.ordinal();
                unit_index = Waystar837P.Units_1.ordinal();
                note_index = Waystar837P.Note_1.ordinal();
                epsdt_index = Waystar837P.EPSDT_1.ordinal();
                rendering_provider_npi_index = Waystar837P.rennpi_1.ordinal();
                l_code_auth_qualifier_index = Waystar837P.renprovnumqual_1.ordinal();
                l_code_auth_number_index = Waystar837P.renprovnum_1.ordinal();
                checker = 0;
                String temp = StringUtil.join(dataFields, "|");
                if (!requests.contains(temp)) {
                    requests.add(temp);
                }
                for (int i = Waystar837P.ServiceFrom_1.ordinal(); i <= Waystar837P.Note_12.ordinal(); i++) {
                    dataFields[i] = "";
                }
            }
            List<String> modifiers = new ArrayList<>();
            if (!StringUtil.isBlank(lCode.getPrescriptionLCode().getModifier1())) {
                modifiers.add(lCode.getPrescriptionLCode().getModifier1());
            }
            if (!StringUtil.isBlank(lCode.getPrescriptionLCode().getModifier2())) {
                modifiers.add(lCode.getPrescriptionLCode().getModifier2());
            }
            if (!StringUtil.isBlank(lCode.getPrescriptionLCode().getModifier3())) {
                modifiers.add(lCode.getPrescriptionLCode().getModifier3());
            }
            if (!StringUtil.isBlank(lCode.getPrescriptionLCode().getModifier4())) {
                modifiers.add(lCode.getPrescriptionLCode().getModifier4());
            }

            Map<String, String> pointers = new HashMap<>();
            int alph_index = 0;
            for (int i = Waystar837P.DiagCodeA.ordinal(); i <= Waystar837P.DiagCodeL.ordinal(); i++) {
                String[] alph = {"A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L"};
                if (!StringUtil.isBlank(dataFields[i])) {
                    pointers.put(dataFields[i], alph[alph_index]);
                    alph_index++;
                } else {
                    break;
                }
            }
            java.sql.Date dos = lCode.getPrescriptionLCode().getDateOfService() != null && lCode.getPrescriptionLCode().getRentalStatus() != null ? lCode.getPrescriptionLCode().getDateOfService() : claim.getDateOfService();
            java.sql.Date dose = lCode.getPrescriptionLCode().getDateOfServiceEnd() != null && lCode.getPrescriptionLCode().getRentalStatus() != null ? lCode.getPrescriptionLCode().getDateOfServiceEnd() : claim.getDateOfService();
            dataFields[dos_index] = DateUtil.getStringDate(dos, Constants.WAYSTAR_DATE_FORMAT);
            dataFields[svcto_index] = DateUtil.getStringDate(dose, Constants.WAYSTAR_DATE_FORMAT);
            dataFields[pos_index] = lCode.getPrescriptionLCode().getPos() != null ? lCode.getPrescriptionLCode().getPos().getValue() : "";
            dataFields[emg_index] = lCode.getPrescriptionLCode().getEmergency() ? "Y" : "";
            dataFields[l_code_index] = lCode.getPrescriptionLCode().getLCode().getName();
            dataFields[modifiers_index] = modifiers.size() > 0 ? StringUtil.join(modifiers, " ") : "";
            dataFields[diagnosis_pointer_index] = loadDiagnosisPointers(lCode.getPrescriptionLCode(), pointers);
            dataFields[charge_index] = lCode.getBillingFee().multiply(BigDecimal.valueOf(lCode.getPrescriptionLCode().getQuantity())).toPlainString();
//            dataFields[charge_index] = lCode.getTotalCharge().toPlainString();
            dataFields[unit_index] = lCode.getPrescriptionLCode().getQuantity() != null ? lCode.getPrescriptionLCode().getQuantity().toString() : "";
            dataFields[epsdt_index] = lCode.getPrescriptionLCode().getEspdt();
            String npi = "";
            switch (renderingProviderOtherId) {
                case "practitioner_npi":
                    if (lCode.getPrescriptionLCode().getPrescription().getTreatingPractitioner() == null)
                        prescriptionService.loadForeignKeys(lCode.getPrescriptionLCode().getPrescription());
                    if (lCode.getPrescriptionLCode().getPrescription().getTreatingPractitioner() != null) {
                        npi = lCode.getPrescriptionLCode().getPrescription().getTreatingPractitioner().getNpi();
                    }
                    break;
                case "pcp_npi":
                    npi = lCode.getPrescriptionLCode().getPrescription().getPrimaryCarePhysician().getNpi();
                    break;
                case "branch_other_id_1":
                    npi = branch.getOtherId1();
                    break;
                case "branch_other_id_2":
                    npi = branch.getOtherId2();
                    break;
                case "branch_npi":
                    npi = branch.getNpi();
                    break;
            }
            dataFields[rendering_provider_npi_index] = npi;
            dataFields[note_index] = lCode.getPrescriptionLCode().getLCodeJustification();
            dataFields[l_code_auth_qualifier_index] = !StringUtil.isBlank(lCode.getAuthNumber()) ? "G1" : "";
            dataFields[l_code_auth_number_index] = !StringUtil.isBlank(lCode.getAuthNumber()) ? lCode.getAuthNumber() : "";

            dos_index += 15;
            svcto_index += 15;
            pos_index += 15;
            emg_index += 15;
            l_code_index += 15;
            modifiers_index += 15;
            diagnosis_pointer_index += 15;
            charge_index += 15;
            unit_index += 15;
            epsdt_index += 15;
            rendering_provider_npi_index += 15;
            note_index += 15;
            l_code_auth_qualifier_index += 15;
            l_code_auth_number_index += 15;
            checker++;
        }
        String temp = StringUtil.join(dataFields, "|");
        if (!requests.contains(temp)) {
            requests.add(temp);
        }
    }

    public void populateZirmedFacilityDataFields(Branch branch, String facilityName, Map<String, String> claimDefaults, Prescription prescription, String[] dataFields) {
        if (branch.getHideServiceFacilityLocation() != null && branch.getHideServiceFacilityLocation()) {
            dataFields[Waystar837P.FacilityName.ordinal()] = "";
            dataFields[Waystar837P.FacilityAdd1.ordinal()] = "";
            dataFields[Waystar837P.FacilityCity.ordinal()] = "";
            dataFields[Waystar837P.FacilityState.ordinal()] = "";
            dataFields[Waystar837P.FacilityZip.ordinal()] = "";
            dataFields[Waystar837P.FacilityProvNum.ordinal()] = "";
        } else if (prescription.getUseAddress() != null && prescription.getUseAddress() && "other".equals(prescription.getDeliveryLocation())) {
            String[] array = prescription.getDeliveryLocationAddress().split(",");
            dataFields[Waystar837P.FacilityName.ordinal()] = array.length > 0 ? array[0] : "";
            dataFields[Waystar837P.FacilityAdd1.ordinal()] = array.length > 1 ? array[1] : "";
            dataFields[Waystar837P.FacilityCity.ordinal()] = array.length > 2 ? array[2] : "";
            dataFields[Waystar837P.FacilityState.ordinal()] = array.length > 3 ? array[3] : "";
            dataFields[Waystar837P.FacilityZip.ordinal()] = array.length > 4 ? array[4] : "";
            dataFields[Waystar837P.FacilityNPI.ordinal()] = claimDefaults.get("provider_information").equals("user_credentials") && prescription.getTreatingPractitioner().getNpi() != null ? prescription.getTreatingPractitioner().getNpi() : branch.getNpi();
        } else if (prescription.getUseAddress() != null && prescription.getUseAddress() && "patient_alternate_address".equals(prescription.getDeliveryLocation())) {
            Patient patient = prescription.getPatient();
            FullName PatientFullName = new FullName(patient.getFirstName(), patient.getMiddleName(), patient.getLastName());
            dataFields[Waystar837P.FacilityName.ordinal()] = StringUtil.formatName(PatientFullName, "LFMi", true);
            dataFields[Waystar837P.FacilityAdd1.ordinal()] = patient.getStreetAddress2();
            dataFields[Waystar837P.FacilityCity.ordinal()] = patient.getCity2();
            dataFields[Waystar837P.FacilityState.ordinal()] = patient.getState2();
            dataFields[Waystar837P.FacilityZip.ordinal()] = patient.getZipcode2();
            dataFields[Waystar837P.FacilityNPI.ordinal()] = claimDefaults.get("provider_information").equals("user_credentials") && prescription.getTreatingPractitioner().getNpi() != null ? prescription.getTreatingPractitioner().getNpi() : branch.getNpi();
        } else if (prescription.getUseAddress() != null && prescription.getUseAddress() && "patient_address".equals(prescription.getDeliveryLocation())) {
            Patient patient = prescription.getPatient();
            FullName PatientFullName = new FullName(patient.getFirstName(), patient.getMiddleName(), patient.getLastName());
            dataFields[Waystar837P.FacilityName.ordinal()] = StringUtil.formatName(PatientFullName, "LFMi", true);
            dataFields[Waystar837P.FacilityAdd1.ordinal()] = patient.getStreetAddress();
            dataFields[Waystar837P.FacilityCity.ordinal()] = patient.getCity();
            dataFields[Waystar837P.FacilityState.ordinal()] = patient.getState();
            dataFields[Waystar837P.FacilityZip.ordinal()] = patient.getZipcode();
            dataFields[Waystar837P.FacilityNPI.ordinal()] = claimDefaults.get("provider_information").equals("user_credentials") && prescription.getTreatingPractitioner().getNpi() != null ? prescription.getTreatingPractitioner().getNpi() : branch.getNpi();
        } else if (prescription.getUseAddress() != null && StringUtils.isNumeric(prescription.getDeliveryLocation()) && prescription.getUseAddress()) {
            DeliveryLocation deliveryLocation = prescription.getDeliveryLocation() != null && !prescription.getDeliveryLocation().equals("") ? deliveryLocationRepository.findById(Long.valueOf(prescription.getDeliveryLocation())).get() : null;
            dataFields[Waystar837P.FacilityName.ordinal()] = deliveryLocation != null ? deliveryLocation.getName() : "";
            dataFields[Waystar837P.FacilityAdd1.ordinal()] = deliveryLocation != null ? deliveryLocation.getStreetAddress() : "";
            dataFields[Waystar837P.FacilityCity.ordinal()] = deliveryLocation != null ? deliveryLocation.getCity() : "";
            dataFields[Waystar837P.FacilityState.ordinal()] = deliveryLocation != null ? deliveryLocation.getState() : "";
            dataFields[Waystar837P.FacilityZip.ordinal()] = deliveryLocation != null ? deliveryLocation.getZipcode() : "";
            dataFields[Waystar837P.FacilityNPI.ordinal()] = deliveryLocation != null ? deliveryLocation.getNpi() : "";
        } else {
            dataFields[Waystar837P.FacilityName.ordinal()] = facilityName;
            dataFields[Waystar837P.FacilityAdd1.ordinal()] = branch.getStreetAddress();
            dataFields[Waystar837P.FacilityCity.ordinal()] = branch.getCity();
            dataFields[Waystar837P.FacilityState.ordinal()] = branch.getState();
            dataFields[Waystar837P.FacilityZip.ordinal()] = branch.getZipcode();
            dataFields[Waystar837P.FacilityNPI.ordinal()] = branch.getNpi();
//                    claimDefaults.get("provider_information").equals("user_credentials") && prescription.getTreatingPractitioner().getNpi() != null ? prescription.getTreatingPractitioner().getNpi() : b.getNpi();
        }
    }

    public void loadSubscriberFields(Long patientId, Long prescriptionId, Long patientInsuranceId, Long claimId, PatientInsurance pi, String[] dataFields) {
        if (pi == null) {
            String message = "Error - patient insurance is null for patient insurance id = " + patientInsuranceId +
                    ", for patient id = " + patientId +
                    ", for prescription id = " + prescriptionId +
                    ", for claim id = " + claimId +
                    ", for tenant = " + TenantContext.getCurrentTenant();
            log.error(message);
        } else if (pi.getInsuranceNumber() == null) {
            String message = "Error - patient insurance, insurance number is null for patient insurance id = " + patientInsuranceId +
                    ", for patient id = " + patientId +
                    ", for prescription id = " + prescriptionId +
                    ", for claim id = " + claimId +
                    ", for tenant = " + TenantContext.getCurrentTenant();
            log.error(message);
        }
        dataFields[Waystar837P.SbrID.ordinal()] = pi.getInsuranceNumber();
        dataFields[Waystar837P.SbrLName.ordinal()] = pi.getLastName();
        dataFields[Waystar837P.SbrFName.ordinal()] = pi.getFirstName();
        dataFields[Waystar837P.SbrMName.ordinal()] = pi.getMiddleName();
        dataFields[Waystar837P.SbrAddr.ordinal()] = pi.getStreetAddress();
        dataFields[Waystar837P.SbrCity.ordinal()] = pi.getCity();
        dataFields[Waystar837P.SbrState.ordinal()] = pi.getState();
        dataFields[Waystar837P.SbrZip.ordinal()] = pi.getZipcode();
        dataFields[Waystar837P.SbrPhone.ordinal()] = StringUtil.formatPhone(pi.getPhoneNumber());
    }

    private void loadOtherSubscriberFields(Long otherPatientInsuranceId, String[] dataFields) {
        if (otherPatientInsuranceId != null) {
            PatientInsurance other = patientInsuranceService.findOne(otherPatientInsuranceId);
            dataFields[Waystar837P.OtherSbrFName.ordinal()] = other.getFirstName();
            dataFields[Waystar837P.OtherSbrMName.ordinal()] = other.getMiddleName();
            dataFields[Waystar837P.OtherSbrLName.ordinal()] = other.getLastName();
            dataFields[Waystar837P.OtherSbrPolicyGroup.ordinal()] = other.getInsuranceNumber();
            dataFields[Waystar837P.OtherInsPlanProgramName.ordinal()] = other.getInsuranceCompany().getName();
        }
    }

    private String loadDiagnosisPointers(Prescription_L_Code lCode, Map<String, String> pointers) {
        String result = "";
        if (lCode.getDiagnosisCode1() != null) {
            String t = pointers.get(lCode.getDiagnosisCode1().getCode());
            if (!StringUtil.isBlank(t)) result = result.concat(t);
        }
        if (lCode.getDiagnosisCode2() != null) {
            String t = pointers.get(lCode.getDiagnosisCode2().getCode());
            if (!StringUtil.isBlank(t)) result = result.concat(t);
        }
        if (lCode.getDiagnosisCode3() != null) {
            String t = pointers.get(lCode.getDiagnosisCode3().getCode());
            if (!StringUtil.isBlank(t)) result = result.concat(t);
        }
        if (lCode.getDiagnosisCode4() != null) {
            String t = pointers.get(lCode.getDiagnosisCode4().getCode());
            if (!StringUtil.isBlank(t)) result = result.concat(t);
        }
        return result;
    }

    public ClaimTotalsReportDTO getClaimTotalsReportDTOList(java.util.Date startDate, java.util.Date endDate) {
        String dateStart = DateUtil.getStringDate(startDate, DF_YYYY_MM_DD_HH_MM);
        String dateEnd = DateUtil.getStringDate(DateUtil.getEndOfDay(endDate), DF_YYYY_MM_DD_HH_MM);
        ClaimTotalsReportDTO result = new ClaimTotalsReportDTO();
        List<Object[]> sqlResult = claimRepository.getTotalBilledReport(dateStart, dateEnd);
        Object[] temp = sqlResult.get(0);
        result.setClaimCount(Long.parseLong(temp[0].toString()));
        result.setTotalAllowable(temp[1] != null ? BigDecimal.valueOf(Double.valueOf(temp[1].toString())) : BigDecimal.ZERO);
        result.setTotalBillable(temp[2] != null ? BigDecimal.valueOf(Double.valueOf(temp[2].toString())).add(BigDecimal.valueOf(Double.valueOf(temp[3].toString()))) : BigDecimal.ZERO);
        result.setTotalPatientResponsibility(temp[3] != null ? BigDecimal.valueOf(Double.valueOf(temp[3].toString())) : BigDecimal.ZERO);
        return result;
    }

    public Map<String, Object> claimsSummary(Date startDate, Date endDate, Long branchId, String dateType) {
        // Claim submissions
        // L Codes (including categories & charges)
        Map<String, Object> results = new HashMap<>();
        try {
            List<ClaimsSummaryDTO> claimResults = new ArrayList<>();
            List<Claim> claims = dateType.equals("submission") ? getActiveClaimsByFirstClaimSubmissionDate(startDate, endDate) : claimRepository.findByDateOfServiceBetween(startDate, endDate);
            BigDecimal insuranceBilled = BigDecimal.ZERO;
            BigDecimal insuranceBalance = BigDecimal.ZERO;
            BigDecimal insurancePaid = BigDecimal.ZERO;
            BigDecimal patientBilled = BigDecimal.ZERO;
            BigDecimal patientBalance = BigDecimal.ZERO;
            BigDecimal patientPaid = BigDecimal.ZERO;
            ArrayList<Long> prescriptionIds = new ArrayList<>();
            for (Claim claim : claims) {
                if (claim.getPrescription().getActive()) {
                    if (branchId == null || claim.getPrescription().getPatient().getPrimaryBranchId().equals(branchId)) {

                        if (!contains(prescriptionIds, claim.getPrescriptionId())) {
                            prescriptionIds.add(claim.getPrescriptionId());
                            insuranceBilled = insuranceBilled.add(claim.getTotalClaimAmount());
                        }
                        insuranceBalance = insuranceBalance.add(claim.getTotalClaimBalance());
                        insurancePaid = insurancePaid.add(claim.getTotalClaimPaid());
                        patientBilled = patientBilled.add(claim.getTotalPtResponsibilityAmount());
                        patientBalance = patientBalance.add(claim.getTotalPtResponsibilityBalance());
                        patientPaid = patientPaid.add(claim.getTotalPtResponsibilityPaid());
//                    List<ClaimSubmission> submissions = claimSubmissionService.findByClaimId(claim.getId());
                        InsuranceVerification insuranceVerification = insuranceVerificationService.findByPatientInsuranceIdAndPrescriptionId(
                                claim.getPatientInsuranceId(), claim.getPrescriptionId());
                        List<InsuranceVerification_L_Code> ivlcs = insuranceVerificationLCodeService.findByInsuranceVerificationId(
                                insuranceVerification.getId());
                        List<IvlcDTO> ivlcDTOS = new ArrayList<>();
                        Map<Long, BigDecimal> totalPaidMap = new HashedMap<>();
                        List<AppliedPayment> appliedPayments = appliedPaymentRepository.findByClaimId(claim.getId());
                        for (AppliedPayment ap : appliedPayments) {
                            List<AppliedPayment_L_Code> aplcs = appliedPaymentLCodeRepository.findByAppliedPaymentId(ap.getId());
                            for (AppliedPayment_L_Code aplc : aplcs) {
                                Long plcId = aplc.getPrescriptionLCodeId();
                                BigDecimal paid = totalPaidMap.get(plcId) == null ? BigDecimal.ZERO : totalPaidMap.get(plcId);
                                paid = paid.add(aplc.getAmount());
                                totalPaidMap.put(plcId, paid);
                            }
                        }
                        for (InsuranceVerification_L_Code ivlc : ivlcs) {
                            IvlcDTO ivlcDTO = new IvlcDTO(ivlc);
                            BigDecimal t = totalPaidMap.get(ivlc.getPrescriptionLCodeId());
                            ivlcDTO.setTotalPaid(t == null ? BigDecimal.ZERO : t);
                            ivlcDTOS.add(ivlcDTO);
                        }
                        ClaimsSummaryDTO claimsSummaryDTO = new ClaimsSummaryDTO();
                        User treatingPractitioner = userService.findOne(
                                claim.getPrescription().getTreatingPractitionerId());
                        claim.getPrescription().setTreatingPractitioner(treatingPractitioner);
                        claimsSummaryDTO.setClaimsSummarySlimClaimDataDTO(populateClaimsSummarySlimClaimDataDTO(claim));
//                    claimsSummaryDTO.setSubmissions(submissions);
                        //                List<InsuranceVerification_L_Code> claimIvlcs = insuranceVerificationLCodeService.getIvlcsByClaimId(claimInsuranceVerificationLCodesDTO.getClaim().getId());
//                for (InsuranceVerification_L_Code ivlc : claimIvlcs) {
//                    IvlcDTO ivlcdto = new IvlcDTO(ivlc);
//                    dtos.add(ivlcdto);
//                }
//                dto.setIvlcs(dtos);
                        claimsSummaryDTO.setIvlcs(ivlcDTOS);
                        claimResults.add(claimsSummaryDTO);
                    }
                }
            }

            results.put("insuranceBilled", insuranceBilled);
            results.put("insuranceBalance", insuranceBalance);
            results.put("insurancePaid", insurancePaid);

            results.put("patientBilled", patientBilled);
            results.put("patientBalance", patientBalance);
            results.put("patientPaid", patientPaid);

            results.put("claimsSummaryDTOs", claimResults);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return results;
    }

    public ClaimsSummarySlimClaimDataDTO populateClaimsSummarySlimClaimDataDTO(Claim claim) {
        ClaimsSummarySlimClaimDataDTO dto = new ClaimsSummarySlimClaimDataDTO();
        Prescription rx = claim.getPrescription(); // shortcut
        dto.setClaimId(claim.getId());
        dto.setPrescriptionId(claim.getPrescriptionId());
        dto.setPatientId(rx.getPatientId());
        dto.setDeviceTypeName(rx.getDeviceType().getName());
        dto.setPatientFirstName(rx.getPatient().getFirstName());
        dto.setPatientMiddleName(rx.getPatient().getMiddleName());
        dto.setPatientLastName(rx.getPatient().getLastName());
        dto.setClaimPatientInsuranceCompanyName(claim.getPatientInsurance().getInsuranceCompany().getName());
        dto.setDateOfService(claim.getDateOfService());
        dto.setPrescriptionDeliveredOn(rx.getDeliveredOn());
        dto.setOrthoticOrProsthetic(rx.getDeviceType().getOrthoticOrProsthetic());
        dto.setGlAccount(rx.getPatient().getPrimaryBranch().getGlAccount());
        dto.setFacilityName(rx.getFacility() != null ? rx.getFacility().getName() : null);
        dto.setPatientPrimaryBranchName(rx.getPatient().getPrimaryBranch().getName());
        dto.setClaimNymblStatusName(claim.getNymblStatus() != null ? claim.getNymblStatus().getName() : null);
        dto.setTotalClaimAmount(claim.getTotalClaimAmount());
        dto.setTotalClaimBalance(claim.getTotalClaimBalance());
        dto.setTotalClaimPaid(claim.getTotalClaimPaid());
        dto.setTotalPtResponsibilityAmount(claim.getTotalPtResponsibilityAmount());
        dto.setTotalPtResponsibilityBalance(claim.getTotalPtResponsibilityBalance());
        dto.setTotalPtResponsibilityPaid(claim.getTotalPtResponsibilityPaid());

        dto.setTreatingPractitionerId(rx.getTreatingPractitionerId());
        dto.setTreatingPractitionerFirstName(rx.getTreatingPractitioner() != null ? rx.getTreatingPractitioner().getFirstName() : null);
        dto.setTreatingPractitionerMiddleName(rx.getTreatingPractitioner() != null ? rx.getTreatingPractitioner().getMiddleName() : null);
        dto.setTreatingPractitionerLastName(rx.getTreatingPractitioner() != null ? rx.getTreatingPractitioner().getLastName() : null);
        dto.setTreatingPractitionerCredentials(rx.getTreatingPractitioner() != null ? rx.getTreatingPractitioner().getCredentials() : null);

        dto.setPrimaryCarePhysicianId(rx.getPrimaryCarePhysicianId());
        dto.setPrimaryCarePhysicianFirstName(rx.getPrimaryCarePhysician() != null ? rx.getPrimaryCarePhysician().getFirstName() : null);
        dto.setPrimaryCarePhysicianMiddleName(rx.getPrimaryCarePhysician() != null ? rx.getPrimaryCarePhysician().getMiddleName() : null);
        dto.setPrimaryCarePhysicianLastName(rx.getPrimaryCarePhysician() != null ? rx.getPrimaryCarePhysician().getLastName() : null);
        dto.setPrimaryCarePhysicianCredentials(rx.getPrimaryCarePhysician() != null ? rx.getPrimaryCarePhysician().getCredentials() : null);

        dto.setReferringPhysicianId(rx.getReferringPhysicianId());
        dto.setReferringPhysicianFirstName(rx.getReferringPhysician() != null ? rx.getReferringPhysician().getFirstName() : null);
        dto.setReferringPhysicianMiddleName(rx.getReferringPhysician() != null ? rx.getReferringPhysician().getMiddleName() : null);
        dto.setReferringPhysicianLastName(rx.getReferringPhysician() != null ? rx.getReferringPhysician().getLastName() : null);
        dto.setReferringPhysicianCredentials(rx.getReferringPhysician() != null ? rx.getReferringPhysician().getCredentials() : null);

        dto.setTherapistId(rx.getTherapistId());
        dto.setTherapistFirstName(rx.getTherapist() != null ? rx.getTherapist().getFirstName() : null);
        dto.setTherapistMiddleName(rx.getTherapist() != null ? rx.getTherapist().getMiddleName() : null);
        dto.setTherapistLastName(rx.getTherapist() != null ? rx.getTherapist().getLastName() : null);
        dto.setTherapistCredentials(rx.getTherapist() != null ? rx.getTherapist().getCredentials() : null);

        return dto;
    }

    public String claimsSummaryPivotTableExport(Date startDate, Date endDate, Long branchId, String dateType) {
        String data = "Claim #,Prescription #,Branch,Device Name,Device Type,Treating Practitioner,Date Delivered," +
                "Insurance" +
                " " +
                "Company,Status,Ins. Billed,Ins. Balance,Ins. Paid,Pat. Billed,Pat. Balance,Pat. Paid,L-Code," +
                "Category,Mod 1,Mod 2,Mod 3,Mod 4,Total Allowable,Total Charge \n";
        Map<String, Object> claimsSummaryResults = claimsSummary(startDate, endDate, branchId, dateType);
        List<ClaimsSummaryDTO> claimsSummaryDTOS = (List<ClaimsSummaryDTO>) claimsSummaryResults.get("claimsSummaryDTOs");
        ArrayList<Long> prescriptionIds = new ArrayList<>();

        for (ClaimsSummaryDTO csDTO : claimsSummaryDTOS) {

            String claimId = csDTO.getClaimsSummarySlimClaimDataDTO().getClaimId().toString();
            String prescriptionId = csDTO.getClaimsSummarySlimClaimDataDTO().getPrescriptionId().toString();
            String branch = csDTO.getClaimsSummarySlimClaimDataDTO().getPatientPrimaryBranchName().replace(",", ".");
            String deviceName = csDTO.getClaimsSummarySlimClaimDataDTO().getDeviceTypeName();
            String deviceType = csDTO.getClaimsSummarySlimClaimDataDTO().getOrthoticOrProsthetic();
            String treatingPractitioner = "";
            if (csDTO.getClaimsSummarySlimClaimDataDTO().getTreatingPractitionerFirstName() != null && csDTO.getClaimsSummarySlimClaimDataDTO().getTreatingPractitionerLastName() != null) {
                treatingPractitioner = csDTO.getClaimsSummarySlimClaimDataDTO().getTreatingPractitionerFirstName()
                        .concat(" ")
                        .concat(csDTO.getClaimsSummarySlimClaimDataDTO().getTreatingPractitionerLastName());
            }
            String dateOfService = csDTO.getClaimsSummarySlimClaimDataDTO().getDateOfService() != null ? csDTO.getClaimsSummarySlimClaimDataDTO().getDateOfService().toString() : "";
            String insuranceCompany = csDTO.getClaimsSummarySlimClaimDataDTO().getClaimPatientInsuranceCompanyName();
//            String claimStatus = csDTO.getClaim().getStatus();
            String nymblStatus = csDTO.getClaimsSummarySlimClaimDataDTO().getClaimNymblStatusName() != null ? csDTO.getClaimsSummarySlimClaimDataDTO().getClaimNymblStatusName() : "";

            String insBilled = "0.00";
            if (!contains(prescriptionIds, csDTO.getClaimsSummarySlimClaimDataDTO().getPrescriptionId())) {
                prescriptionIds.add(csDTO.getClaimsSummarySlimClaimDataDTO().getPrescriptionId());
                insBilled = csDTO.getClaimsSummarySlimClaimDataDTO().getTotalClaimAmount().toString();
            }

            String insBalance = csDTO.getClaimsSummarySlimClaimDataDTO().getTotalClaimBalance().toString();
            String insPaid = csDTO.getClaimsSummarySlimClaimDataDTO().getTotalClaimPaid().toString();
            String patBilled = csDTO.getClaimsSummarySlimClaimDataDTO().getTotalPtResponsibilityAmount().toString();
            String patBalance = csDTO.getClaimsSummarySlimClaimDataDTO().getTotalPtResponsibilityBalance().toString();
            String patPaid = csDTO.getClaimsSummarySlimClaimDataDTO().getTotalPtResponsibilityPaid().toString();

            data = data.concat(claimId);
            data = data.concat(",").concat(prescriptionId);
            data = data.concat(",").concat(branch);
            data = data.concat(",").concat(deviceName);
            data = data.concat(",").concat(deviceType);
            data = data.concat(",").concat(treatingPractitioner);
            data = data.concat(",").concat(dateOfService);
            data = data.concat(",").concat(insuranceCompany);
            data = data.concat(",").concat(nymblStatus);
            data = data.concat(",").concat(insBilled);
            data = data.concat(",").concat(insBalance);
            data = data.concat(",").concat(insPaid);
            data = data.concat(",").concat(patBilled);
            data = data.concat(",").concat(patBalance);
            data = data.concat(",").concat(patPaid);
            data = data.concat(",").concat("");
            data = data.concat(",").concat("");
            data = data.concat(",").concat("");
            data = data.concat(",").concat("");
            data = data.concat(",").concat("");
            data = data.concat(",").concat("");
            data = data.concat(",").concat("");
            data = data.concat(",").concat("");
            data = data.concat("\n");

            for (InsuranceVerification_L_Code ivlc : csDTO.getIvlcs()) {
                data = data.concat(claimId);
                data = data.concat(",").concat(prescriptionId);
                data = data.concat(",").concat(branch);
                data = data.concat(",").concat(deviceName);
                data = data.concat(",").concat(deviceType);
                data = data.concat(",").concat(treatingPractitioner);
                data = data.concat(",").concat(dateOfService);
                data = data.concat(",").concat(insuranceCompany);
                data = data.concat(",").concat(nymblStatus);
                data = data.concat(",").concat("");
                data = data.concat(",").concat("");
                data = data.concat(",").concat("");
                data = data.concat(",").concat("");
                data = data.concat(",").concat("");
                data = data.concat(",").concat("");
                data = data.concat(",").concat(ivlc.getPrescriptionLCode().getLCode().getName());
                data = data.concat(",").concat(ivlc.getPrescriptionLCode().getLCode().getLCodeCategory() != null ? ivlc.getPrescriptionLCode().getLCode().getLCodeCategory().getCategory() : "");
                data = data.concat(",").concat(ivlc.getPrescriptionLCode().getModifier1() != null ? ivlc.getPrescriptionLCode().getModifier1() : "");
                data = data.concat(",").concat(ivlc.getPrescriptionLCode().getModifier2() != null ? ivlc.getPrescriptionLCode().getModifier2() : "");
                data = data.concat(",").concat(ivlc.getPrescriptionLCode().getModifier3() != null ? ivlc.getPrescriptionLCode().getModifier3() : "");
                data = data.concat(",").concat(ivlc.getPrescriptionLCode().getModifier4() != null ? ivlc.getPrescriptionLCode().getModifier4() : "");
                data = data.concat(",").concat(ivlc.getTotalAllowable().toString());
                data = data.concat(",").concat(ivlc.getTotalCharge().toString());
                data = data.concat("\n");
            }
        }

        return data;
    }

    public List<Map<String, Object>> outstandingInsuranceBalances(Long branchId) {
        List<Map<String, Object>> results = new ArrayList<>();
        List<Claim> claims;
        if (branchId != null) {
            claims = claimRepository.findByMostRecentClaimAndBranchId(branchId);
        } else {
            claims = claimRepository.findMostRecentClaim();
        }

        List<InsuranceCompany> insuranceCompanies = insuranceCompanyService.findAll();
        InsuranceCompany insuranceTotal = new InsuranceCompany();
        Date juDate = new Date();
        DateTime today = new DateTime(juDate);
        DateTime thirtyDay = today.minusDays(29);
        DateTime sixtyDay = today.minusDays(59);
        DateTime ninetyDay = today.minusDays(89);
        DateTime oneTwentyDay = today.minusDays(119);
        DateTime oneTwentyPlusDay = today.minusDays(120);

        InsuranceBalances insuranceBalances = new InsuranceBalances();
        Map<Long, List<Claim>> greaterThanZeroTotalClaimBalanceInsuranceClaimsByInsuranceIdMap = populateGreaterThanZeroTotalClaimBalanceInsuranceClaimsByInsuranceIdMap(claims, insuranceCompanies);
        List<Claim> greaterThanZeroTotalClaimBalanceInsuranceClaims = populateGreaterThanZeroTotalClaimBalanceInsuranceClaims(greaterThanZeroTotalClaimBalanceInsuranceClaimsByInsuranceIdMap);
        Map<Long, List<ClaimSubmission>> claimIdClaimSubmissionMap = populateClaimIdClaimSubmissionMap(greaterThanZeroTotalClaimBalanceInsuranceClaims);
        populateGreaterThanZeroInsuranceBalancesForInsuranceCompanies(results, insuranceCompanies, thirtyDay, sixtyDay, ninetyDay, oneTwentyDay, oneTwentyPlusDay, insuranceBalances, greaterThanZeroTotalClaimBalanceInsuranceClaimsByInsuranceIdMap, claimIdClaimSubmissionMap);
        insuranceTotal.setName("TOTALS FOR ALL INSURANCE COMPANIES");
        storeFinalInsuranceBalance(results, insuranceTotal, insuranceBalances);
        return results;
    }

    public void populateGreaterThanZeroInsuranceBalancesForInsuranceCompanies(List<Map<String, Object>> results, List<InsuranceCompany> insuranceCompanies, DateTime thirtyDay, DateTime sixtyDay, DateTime ninetyDay, DateTime oneTwentyDay, DateTime oneTwentyPlusDay, InsuranceBalances insuranceBalances, Map<Long, List<Claim>> greaterThanZeroTotalClaimBalanceInsuranceClaimsByInsuranceIdMap, Map<Long, List<ClaimSubmission>> claimIdClaimSubmissionMap) {
        for (InsuranceCompany ic : insuranceCompanies) {
            Map<String, Object> map = new HashMap<>();
            if (greaterThanZeroTotalClaimBalanceInsuranceClaimsByInsuranceIdMap.containsKey(ic.getId())) {
                for (Claim c : greaterThanZeroTotalClaimBalanceInsuranceClaimsByInsuranceIdMap.get(ic.getId())) {
                    ClaimDTO dto = new ClaimDTO(c);
                    ClaimSubmission submission = getClaimSubmissionWithMaxId(claimIdClaimSubmissionMap, dto);
                    if (submission == null) {
                        insuranceBalances.noSubmissionBalance = insuranceBalances.noSubmissionBalance.add(dto.getClaim().getTotalClaimBalance());
                        insuranceBalances.totalNoSubmissionBalance = insuranceBalances.totalNoSubmissionBalance.add(dto.getClaim().getTotalClaimBalance());
                    } else {
                        populateInsuranceBalances(thirtyDay, sixtyDay, ninetyDay, oneTwentyDay, oneTwentyPlusDay, insuranceBalances, dto, submission);
                    }
                    insuranceBalances.insuranceBalance = insuranceBalances.insuranceBalance.add(dto.getClaim().getTotalClaimBalance());
                    insuranceBalances.insuranceClaimsWithBalance.add(dto);
                    insuranceBalances.totalInsuranceBalance = insuranceBalances.totalInsuranceBalance.add(dto.getClaim().getTotalClaimBalance());
                }
            }
            storeNotZeroInsuranceBalances(results, insuranceBalances, ic, map);
            initializeInsuranceBalancesToZero(insuranceBalances);
        }
    }

    public void populateInsuranceBalances(DateTime thirtyDay, DateTime sixtyDay, DateTime ninetyDay, DateTime oneTwentyDay, DateTime oneTwentyPlusDay, InsuranceBalances insuranceBalances, ClaimDTO dto, ClaimSubmission submission) {
        dto.setSubmissionDate(submission.getSubmissionDate());
        DateTime submissionDate = new DateTime(submission.getSubmissionDate());
        if (submissionDate.compareTo(thirtyDay) >= 0) {
            insuranceBalances.thirtyDayBalance = insuranceBalances.thirtyDayBalance.add(dto.getClaim().getTotalClaimBalance());
            insuranceBalances.totalThirtyDayBalance = insuranceBalances.totalThirtyDayBalance.add(dto.getClaim().getTotalClaimBalance());
        } else if (submissionDate.compareTo(sixtyDay) >= 0 && submissionDate.compareTo(thirtyDay) < 0) {
            insuranceBalances.sixtyDayBalance = insuranceBalances.sixtyDayBalance.add(dto.getClaim().getTotalClaimBalance());
            insuranceBalances.totalSixtyDayBalance = insuranceBalances.totalSixtyDayBalance.add(dto.getClaim().getTotalClaimBalance());
        } else if (submissionDate.compareTo(ninetyDay) >= 0 && submissionDate.compareTo(sixtyDay) < 0) {
            insuranceBalances.ninetyDayBalance = insuranceBalances.ninetyDayBalance.add(dto.getClaim().getTotalClaimBalance());
            insuranceBalances.totalNinetyDayBalance = insuranceBalances.totalNinetyDayBalance.add(dto.getClaim().getTotalClaimBalance());
        } else if ((submissionDate.compareTo(oneTwentyDay) >= 0 || submissionDate.compareTo(
                oneTwentyPlusDay) > 0) && submissionDate.compareTo(ninetyDay) < 0) {
            insuranceBalances.oneTwentyDayBalance = insuranceBalances.oneTwentyDayBalance.add(dto.getClaim().getTotalClaimBalance());
            insuranceBalances.totalOneTwentyDayBalance = insuranceBalances.totalOneTwentyDayBalance.add(dto.getClaim().getTotalClaimBalance());
        } else if (submissionDate.compareTo(oneTwentyPlusDay) <= 0) {
            insuranceBalances.oneTwentyPlusDayBalance = insuranceBalances.oneTwentyPlusDayBalance.add(dto.getClaim().getTotalClaimBalance());
            insuranceBalances.totalOneTwentyPlusDayBalance = insuranceBalances.totalOneTwentyPlusDayBalance.add(dto.getClaim().getTotalClaimBalance());
        }
    }

    public ClaimSubmission getClaimSubmissionWithMaxId(Map<Long, List<ClaimSubmission>> claimIdClaimSubmissionMap, ClaimDTO dto) {
        ClaimSubmission submission = null;
        List<ClaimSubmission> submissionsList = claimIdClaimSubmissionMap.get(dto.getClaim().getId());
        if (submissionsList.size() > 1) {
            submission =
                    submissionsList
                            .stream()
                            .max(Comparator.comparingLong(ClaimSubmission::getId)).get();
        } else if (submissionsList.size() == 1) {
            submission = submissionsList.get(0);
        }
        return submission;
    }

    public void storeFinalInsuranceBalance(List<Map<String, Object>> results, InsuranceCompany insuranceTotal, InsuranceBalances insuranceBalances) {
        Map<String, Object> map = new HashMap<>();
        map.put("insuranceCompany", insuranceTotal);
        map.put("insuranceBalance", insuranceBalances.totalInsuranceBalance);
        map.put("thirtyDayBalance", insuranceBalances.totalThirtyDayBalance);
        map.put("sixtyDayBalance", insuranceBalances.totalSixtyDayBalance);
        map.put("ninetyDayBalance", insuranceBalances.totalNinetyDayBalance);
        map.put("oneTwentyDayBalance", insuranceBalances.totalOneTwentyDayBalance);
        map.put("oneTwentyPlusDayBalance", insuranceBalances.totalOneTwentyPlusDayBalance);
        map.put("noSubmissionBalance", insuranceBalances.totalNoSubmissionBalance);
        results.add(map);
    }

    public void initializeInsuranceBalancesToZero(InsuranceBalances insuranceBalances) {
        insuranceBalances.insuranceBalance = BigDecimal.ZERO;
        insuranceBalances.thirtyDayBalance = BigDecimal.ZERO;
        insuranceBalances.sixtyDayBalance = BigDecimal.ZERO;
        insuranceBalances.ninetyDayBalance = BigDecimal.ZERO;
        insuranceBalances.oneTwentyDayBalance = BigDecimal.ZERO;
        insuranceBalances.oneTwentyPlusDayBalance = BigDecimal.ZERO;
        insuranceBalances.noSubmissionBalance = BigDecimal.ZERO;
        insuranceBalances.insuranceClaimsWithBalance = new ArrayList<>();
    }

    public void storeNotZeroInsuranceBalances(List<Map<String, Object>> results, InsuranceBalances insuranceBalances, InsuranceCompany ic, Map<String, Object> map) {
        if (!insuranceBalances.insuranceBalance.equals(BigDecimal.ZERO)) {
            map.put("insuranceCompany", ic);
            map.put("insuranceBalance", insuranceBalances.insuranceBalance);
            map.put("claimsWithBalance", insuranceBalances.insuranceClaimsWithBalance);
            map.put("thirtyDayBalance", insuranceBalances.thirtyDayBalance);
            map.put("sixtyDayBalance", insuranceBalances.sixtyDayBalance);
            map.put("ninetyDayBalance", insuranceBalances.ninetyDayBalance);
            map.put("oneTwentyDayBalance", insuranceBalances.oneTwentyDayBalance);
            map.put("oneTwentyPlusDayBalance", insuranceBalances.oneTwentyPlusDayBalance);
            map.put("noSubmissionBalance", insuranceBalances.noSubmissionBalance);
            results.add(map);
        }
    }

    public List<Claim> populateGreaterThanZeroTotalClaimBalanceInsuranceClaims(Map<Long, List<Claim>> greaterThanZeroTotalClaimBalanceInsuranceClaimsMap) {
        List<Claim> greaterThanZeroTotalClaimBalanceInsuranceClaims = new ArrayList<>();
        for (Long icId : greaterThanZeroTotalClaimBalanceInsuranceClaimsMap.keySet()) {
            greaterThanZeroTotalClaimBalanceInsuranceClaims.addAll(greaterThanZeroTotalClaimBalanceInsuranceClaimsMap.get(icId));
        }
        return greaterThanZeroTotalClaimBalanceInsuranceClaims;
    }

    public Map<Long, List<ClaimSubmission>> populateClaimIdClaimSubmissionMap(List<Claim> claims) {
        Map<Long, List<ClaimSubmission>> claimIdClaimSubmissionMap = new HashMap<>();
        List<Long> claimIds = new ArrayList<>(claims.size());
        for (Claim claim : claims) {
            claimIdClaimSubmissionMap.put(claim.getId(), new ArrayList<>());
            claimIds.add(claim.getId());
        }

        List<ClaimSubmission> claimSubmissionsList = claimSubmissionService.findByClaimIdIn(claimIds);

        for (ClaimSubmission claimSubmission : claimSubmissionsList) {
            (claimIdClaimSubmissionMap.get(claimSubmission.getClaim().getId())).add(claimSubmission);
        }
        return claimIdClaimSubmissionMap;
    }

    public Map<Long, List<Claim>> populateGreaterThanZeroTotalClaimBalanceInsuranceClaimsByInsuranceIdMap(List<Claim> claims, List<InsuranceCompany> insuranceCompanies) {
        Map<Long, List<Claim>> greaterThanZeroTotalClaimBalanceInsuranceClaimsMap = new HashMap<>(insuranceCompanies.size());
        for (InsuranceCompany ic : insuranceCompanies) {
            for (Claim claim : claims) {
                if (claim.getPatientInsurance().getInsuranceCompanyId().equals(ic.getId()) &&
                        compare(claim.getTotalClaimBalance(), ">", BigDecimal.ZERO)) {
                    if (greaterThanZeroTotalClaimBalanceInsuranceClaimsMap.containsKey(ic.getId())) {
                        (greaterThanZeroTotalClaimBalanceInsuranceClaimsMap.get(ic.getId())).add(claim);
                    } else {
                        List<Claim> claimList = new ArrayList<>();
                        claimList.add(claim);
                        greaterThanZeroTotalClaimBalanceInsuranceClaimsMap.put(ic.getId(), claimList);
                    }
                }
            }
        }
        return greaterThanZeroTotalClaimBalanceInsuranceClaimsMap;
    }

    public void bulkUpdateClaimStatus(List<Long> claimIds, Long nymblStatusId) {
        for (Long claimId : claimIds) {
            Optional<Claim> optionalClaim = claimRepository.findById(claimId);
            if (optionalClaim.isPresent()) {
                Claim claim = optionalClaim.get();
                claim.setNymblStatusId(nymblStatusId);
                save(claim);
            }
        }
    }

    public void reassignClaims(List<Long> claimIds, Long userId) {
        for (Long claimId : claimIds) {
            Optional<Claim> optionalClaim = claimRepository.findById(claimId);
            if (optionalClaim.isPresent()) {
                Claim claim = optionalClaim.get();
                claim.setUserId(userId);
                save(claim);
            }
        }
    }

    /**
     * Validate claims for bulk submission.
     * This method checks if the selected claims can be submitted together in a bulk file.
     *
     * @param claimIds the list of claim IDs to validate
     * @return a list of validation errors, empty if validation passes
     */
    public List<String> validateBulkSubmission(List<Long> claimIds) {
        List<String> errors = new ArrayList<>();

        if (claimIds == null || claimIds.isEmpty()) {
            errors.add("No claims selected for bulk submission");
            return errors;
        }

        if (claimIds.size() < 2) {
            errors.add("Bulk submission requires at least 2 claims");
            return errors;
        }

        // Get all claims in a single batch query for efficiency
        List<Claim> claims = claimRepository.findAllById(claimIds);

        // Check for missing claims
        Set<Long> foundClaimIds = claims.stream().map(Claim::getId).collect(Collectors.toSet());
        for (Long claimId : claimIds) {
            if (!foundClaimIds.contains(claimId)) {
                errors.add("Claim with ID " + claimId + " not found");
            }
        }

        if (claims.isEmpty()) {
            errors.add("No valid claims found");
            return errors;
        }

        // Check if all claims have the same billing branch
        Long billingBranchId = claims.get(0).getBillingBranchId();
        for (Claim claim : claims) {
            if (!billingBranchId.equals(claim.getBillingBranchId())) {
                errors.add("All claims must have the same billing branch");
                break;
            }
        }

        // Check if all claims have the same payer
        Long payerId = null;
        if (claims.get(0).getResponsiblePatientInsurance() != null &&
            claims.get(0).getResponsiblePatientInsurance().getInsuranceCompany() != null) {
            payerId = claims.get(0).getResponsiblePatientInsurance().getInsuranceCompany().getId();
        }

        if (payerId == null) {
            errors.add("First claim has no valid payer");
        } else {
            for (Claim claim : claims) {
                if (claim.getResponsiblePatientInsurance() == null ||
                    claim.getResponsiblePatientInsurance().getInsuranceCompany() == null ||
                    !payerId.equals(claim.getResponsiblePatientInsurance().getInsuranceCompany().getId())) {
                    errors.add("All claims must have the same payer");
                    break;
                }
            }
        }

        return errors;
    }

    /**
     * Validate bulk X12 claims using Waystar Real-Time Rules Engine.
     * This method generates the bulk X12 file and sends it to Waystar for validation.
     *
     * @param claimIds the list of claim IDs to validate
     * @return a map containing validation results from Waystar
     */
    public Map<String, Object> validateBulkX12WithWaystar(List<Long> claimIds) {
        Map<String, Object> result = new HashMap<>();
        List<String> errors = new ArrayList<>();

        try {
            // First do basic validation
            List<String> basicErrors = validateBulkSubmission(claimIds);
            if (!basicErrors.isEmpty()) {
                result.put("valid", false);
                result.put("errors", basicErrors);
                result.put("source", "basic_validation");
                return result;
            }

            // Get the first claim to determine billing branch and credentials
            Claim firstClaim = findOne(claimIds.get(0));
            Branch billingBranch = firstClaim.getBillingBranch();

            // Check if Real-Time Rules Engine is enabled for this branch
            boolean useRealTimeRulesEngine = BooleanUtils.toBooleanDefaultIfNull(billingBranch.getUseRealTimeRulesEngine(), false);
            if (!useRealTimeRulesEngine) {
                result.put("valid", true);
                result.put("message", "Waystar Real-Time Rules Engine is not enabled for this branch");
                result.put("source", "waystar_disabled");
                return result;
            }

            // Check if we have Waystar credentials
            if (billingBranch.getOutClearingHouse() == null ||
                StringUtil.isBlank(billingBranch.getOutClearingHouse().getRestUser()) ||
                StringUtil.isBlank(billingBranch.getOutClearingHouse().getRestPassword())) {
                errors.add("Waystar credentials not configured for billing branch");
                result.put("valid", false);
                result.put("errors", errors);
                result.put("source", "waystar_credentials");
                return result;
            }

            // Generate bulk X12 file
            String timestamp = DateUtil.getStringDate(new Date(), Constants.DF_YYYYMMDDHHmmssSSS);
            List<Factory837Parameters> paramsList = factory837.buildBulk(claimIds, timestamp, billingBranch.getId());

            // Check for X12 generation errors
            List<String> allErrors = new ArrayList<>();
            for (Factory837Parameters params : paramsList) {
                allErrors.addAll(params.getValidationErrors());
            }

            if (!allErrors.isEmpty()) {
                result.put("valid", false);
                result.put("errors", allErrors);
                result.put("source", "x12_generation");
                return result;
            }

            // Build the bulk X12 transaction
            Factory837Parameters firstParams = paramsList.get(0);
            X12ClaimTransaction bulkTransaction = factory837.buildBulkTransaction(firstParams, paramsList);
            String bulkX12Content = bulkTransaction.toX12String();

            // Send to Waystar for validation
            String restUser = billingBranch.getOutClearingHouse().getRestUser();
            String restPassword = billingBranch.getOutClearingHouse().getRestPassword();

            Map<String, String> waystarResponse = waystarAPI.realTimeRulesEngineRun(restUser, restPassword, bulkX12Content);

            // Process Waystar response
            if (waystarResponse.get("Error") != null) {
                errors.add("Waystar validation error: " + waystarResponse.get("Error"));
                result.put("valid", false);
                result.put("errors", errors);
                result.put("source", "waystar_error");
            } else if ("Accept".equals(waystarResponse.get("Status"))) {
                result.put("valid", true);
                result.put("message", "Bulk X12 file passed Waystar validation");
                result.put("source", "waystar_validation");
                result.put("waystarResponse", waystarResponse);
            } else {
                // Validation failed
                errors.add("Waystar validation failed");
                if (waystarResponse.get("ErrorMessages") != null) {
                    errors.add("Waystar errors: " + waystarResponse.get("ErrorMessages"));
                }
                result.put("valid", false);
                result.put("errors", errors);
                result.put("source", "waystar_validation");
                result.put("waystarResponse", waystarResponse);
            }

        } catch (Exception e) {
            // Use System.err since log is not available in this context
            System.err.println("Error validating bulk X12 with Waystar: " + e.getMessage());
            e.printStackTrace();
            errors.add("Error validating with Waystar: " + e.getMessage());
            result.put("valid", false);
            result.put("errors", errors);
            result.put("source", "exception");
        }

        return result;
    }

    /**
     * Send bulk claim files.
     * This method creates a bulk claim job and processes the claims in a batch.
     *
     * @param claimIds the list of claim IDs to process
     * @param billingBranchId the billing branch ID
     * @return a map containing the job ID and status
     * @throws X12Exception if there is an error generating the X12 file
     */
    public Map<String, Object> sendBulkClaimFiles(List<Long> claimIds, Long billingBranchId) throws X12Exception {
        // Validate claims for bulk submission
        List<String> validationErrors = validateBulkSubmission(claimIds);
        if (!validationErrors.isEmpty()) {
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("errors", validationErrors);
            return result;
        }

        // Create a bulk claim job
        BulkClaimJob job = bulkClaimJobService.createJob(claimIds);

        // Start processing in a separate thread with tenant context propagation
        // using Spring's @Async mechanism
        bulkClaimJobService.processBulkClaimJobAsync(job.getJobId(), claimIds, billingBranchId);

        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("jobId", job.getJobId());
        result.put("status", job.getStatus());
        return result;
    }

    /**
     * Process a bulk claim job.
     * This method processes the claims in a batch and updates the job status.
     *
     * @param jobId the job ID
     * @param claimIds the list of claim IDs to process
     * @param billingBranchId the billing branch ID
     * @throws X12Exception if there is an error generating the X12 file
     */
    protected void processBulkClaimJob(String jobId, List<Long> claimIds, Long billingBranchId) throws X12Exception {
        // Add debug logging for tenant context
        log.debug("Processing bulk claim job {} with tenant context: {}",
                 jobId, TenantContext.getCurrentTenant());

        SystemSetting format837 = systemSettingService.findBySectionAndField("claim", "format_837");
        ZonedDateTime today = ZonedDateTime.ofInstant(Instant.now(), ZoneId.of(TenantContext.getUserTimezoneId()));
        String timestamp = DateUtil.getStringDate(new Date(), Constants.DF_YYYYMMDDHHmmssSSS);

        // Get the first claim to determine the payer
        Claim firstClaim = findOne(claimIds.get(0));
        PatientInsurance patientInsurance = firstClaim.getResponsiblePatientInsurance();
        String currentPayer = patientInsurance.getInsuranceCompany().getName();

        // Update job status to processing
        bulkClaimJobService.updateJobProgress(jobId, currentPayer, 0, 0, 0);

        Branch branch;
        if (billingBranchId != null) {
            branch = branchService.findOne(billingBranchId);
        } else {
            branch = branchService.findOne(firstClaim.getBillingBranchId());
        }

        int successfulClaims = 0;
        int failedClaims = 0;

        if ("zirmed".equals(format837.getValue())) {
            // For Zirmed format, we need to create individual claim files
            // Get all claims in a single batch query for efficiency
            List<Claim> claims = claimRepository.findAllById(claimIds);
            Map<Long, Claim> claimMap = claims.stream().collect(Collectors.toMap(claim -> claim.getId(), claim -> claim));

            for (int i = 0; i < claimIds.size(); i++) {
                Long claimId = claimIds.get(i);
                try {
                    Claim claim = claimMap.get(claimId);
                    if (claim == null) {
                        throw new IllegalArgumentException("Claim not found: " + claimId);
                    }

                    List<String> requests837 = create837Requests(claim, branch, claim.getResponsiblePatientInsuranceId(), claim.getOtherPatientInsuranceId());
                    String claimData = StringUtils.join(requests837, "\n");
                    ClaimFile cf = createClaimFile(claim, branch.getId(), claimData, timestamp);
                    createClaimSubmission(claim.getId(), claim.getResponsiblePatientInsuranceId(), cf);
                    claim.setNymblStatusId(21L);
                    save(claim);
                    successfulClaims++;
                } catch (Exception e) {
                    log.error("Error processing claim " + claimId + ": " + e.getMessage(), e);
                    failedClaims++;
                }

                // Update job progress
                bulkClaimJobService.updateJobProgress(jobId, currentPayer, i + 1, successfulClaims, failedClaims);
            }
        } else {
            // For standard format, we can create a bulk X12 file
            try {
                // Build parameters for all claims
                List<Factory837Parameters> paramsList = factory837.buildBulk(claimIds, timestamp, billingBranchId);

                // Check for validation errors
                List<String> allErrors = new ArrayList<>();
                for (Factory837Parameters params : paramsList) {
                    allErrors.addAll(params.getValidationErrors());
                }

                if (!allErrors.isEmpty()) {
                    throw new X12Exception(allErrors);
                }

                // Create the bulk X12 claim
                X12Claim bulkX12Claim = factory837.createBulkX12Claim(paramsList);
                String bulkX12String = bulkX12Claim.toX12String();

                // Store the X12 file content in the bulk claim job
                storeBulkX12File(jobId, bulkX12String, claimIds);

                // Create a single claim file for all claims
                ClaimFile bulkClaimFile = new ClaimFile();
                String filename = "bulk_" + timestamp + "_" + patientInsurance.getInsuranceCompanyId().toString() + ".CLP";
                bulkClaimFile.setFilename(filename);
                bulkClaimFile.setSubmitted(false);
                bulkClaimFile.setClaimId(claimIds.get(0)); // Associate with the first claim
                bulkClaimFile.setContents(bulkX12String);
                bulkClaimFile.setBranchId(branch.getId());
                bulkClaimFile = claimFileService.save(bulkClaimFile);

                // Create claim submissions for each claim
                for (int i = 0; i < claimIds.size(); i++) {
                    Long claimId = claimIds.get(i);
                    try {
                        Claim claim = findOne(claimId);
                        createClaimSubmission(claim.getId(), claim.getResponsiblePatientInsuranceId(), bulkClaimFile);
                        claim.setNymblStatusId(21L);
                        save(claim);
                        successfulClaims++;
                    } catch (Exception e) {
                        log.error("Error processing claim " + claimId + ": " + e.getMessage(), e);
                        failedClaims++;
                    }

                    // Update job progress
                    bulkClaimJobService.updateJobProgress(jobId, currentPayer, i + 1, successfulClaims, failedClaims);
                }
            } catch (X12Exception e) {
                // If there's an X12 exception, mark all claims as failed
                failedClaims = claimIds.size();
                bulkClaimJobService.failJob(jobId, e.getMessage());
                throw e;
            }
        }

        // Complete the job
        bulkClaimJobService.completeJob(jobId, successfulClaims, failedClaims);
    }

    public Claim findByPrescriptionIdAndPatientInsuranceId(Long prescriptionId, Long patientInsuranceId) {
        Claim result = claimRepository.findTop1ByPrescriptionIdAndPatientInsuranceIdOrderByIdDesc(prescriptionId, patientInsuranceId);
        loadForeignKeys(result);
        return result;
    }

    public Claim findByPatientInsuranceId(Long patientInsuranceId) {
        Claim result = claimRepository.findByPatientInsuranceId(patientInsuranceId);
        loadForeignKeys(result);
        return result;
    }

    public List<Claim> getOutstandingPatientResponsibilityBalanceClaims(Long branchId) {
        List<Claim> claimsList;
        if (branchId != null) {
            claimsList = claimRepository.findAllByBranchIdAndTotalPtResponsibilityBalanceIsGreaterThan(branchId, new BigDecimal("0.00", MathContext.DECIMAL64));
        } else {
            claimsList = claimRepository.findAllByTotalPtResponsibilityBalanceIsGreaterThan(new BigDecimal("0.00", MathContext.DECIMAL64));
        }
        loadForeignKeysForClaimResults(claimsList);
        return claimsList;
    }

    public List<Claim> findAllByUpdatedAtBeforeAndTotalClaimBalanceGreaterThanOrTotalPtResponsibilityBalanceGreaterThanAndBranchId(Long branchId) {
        List<Claim> claimList = claimRepository.findAllByUpdatedAtBeforeAndTotalClaimBalanceGreaterThanOrTotalPtResponsibilityBalanceGreaterThanAndBranchId(branchId);
        return claimList;
    }

    public List<Claim> uncollected(Long branchId, Date startDate, Date endDate) {
        List<Claim> claimsList = claimRepository.findUncollectedByBranch(branchId, startDate, endDate);
        loadForeignKeysForClaimResults(claimsList);
        return claimsList;
    }

    public List<AutoPostClaimResponse> getAutoPostClaimResponseByClaimId(Long claimId) {
        return autoPostClaimResponseService.getAllByClaimId(claimId);
    }

    public List<Map<String, String>> getProjectedVsBilled(List<Long> branchIds, boolean isPatientBranch, boolean isPrescriptionBranch) {
        LocalDate firstDayOfMonth = new LocalDate().withDayOfMonth(1);
        LocalDate lastDayOfMonth = new LocalDate().plusMonths(1).withDayOfMonth(1).minusDays(1);

        List<Map<String, String>> branchList = new ArrayList<>();
        BigDecimal allBranchesProjected = BigDecimal.ZERO;
        BigDecimal allBranchesBilled = BigDecimal.ZERO;
        BigDecimal allBranchesMonthlySalesGoal = BigDecimal.ZERO;

        BigDecimal allBranchesBilledMastectomyAllowable = BigDecimal.ZERO;
        BigDecimal allBranchesBilledProstheticAllowable = BigDecimal.ZERO;
        BigDecimal allBranchesBilledOrthoticAllowable = BigDecimal.ZERO;
        BigDecimal allBranchesBilledPedorthicAllowable = BigDecimal.ZERO;
        BigDecimal allBranchesBilledMiscAllowable = BigDecimal.ZERO;

        BigDecimal allBranchesProjectedMastectomyAllowable = BigDecimal.ZERO;
        BigDecimal allBranchesProjectedProstheticAllowable = BigDecimal.ZERO;
        BigDecimal allBranchesProjectedPedorthicAllowable = BigDecimal.ZERO;
        BigDecimal allBranchesProjectedMiscAllowable = BigDecimal.ZERO;
        BigDecimal allBranchesProjectedOrthoticAllowable = BigDecimal.ZERO;

        for (Long branchId : branchIds) {
            Map<String, String> totalsMap = new HashMap<>();
            BranchDto branch = branchDtoRepository.findBranchDtoByIdDto(branchId);
            BigDecimal projectedBilledAmount = BigDecimal.ZERO;
            BigDecimal billedAmount = BigDecimal.ZERO;
            BigDecimal billedMastectomyAllowable = BigDecimal.ZERO;
            BigDecimal billedProstheticAllowable = BigDecimal.ZERO;
            BigDecimal billedOrthoticAllowable = BigDecimal.ZERO;
            BigDecimal billedPedorthicAllowable = BigDecimal.ZERO;
            BigDecimal billedMiscAllowable = BigDecimal.ZERO;

            BigDecimal projectedMastectomyAllowable = BigDecimal.ZERO;
            BigDecimal projectedProstheticAllowable = BigDecimal.ZERO;
            BigDecimal projectedOrthoticAllowable = BigDecimal.ZERO;
            BigDecimal projectedPedorthicAllowable = BigDecimal.ZERO;
            BigDecimal projectedMiscAllowable = BigDecimal.ZERO;

            List<PrescriptionDto> prescriptions;
            if (isPatientBranch) {
                prescriptions = prescriptionDtoService.findPrescriptionByPatientBranchAndSubmissionDate(java.sql.Date.valueOf(String.valueOf(firstDayOfMonth)), java.sql.Date.valueOf(String.valueOf(lastDayOfMonth)), branchId);
            } else if (isPrescriptionBranch) {
                prescriptions = prescriptionDtoService.findPrescriptionByPrescriptionBranchAndSubmissionDate(java.sql.Date.valueOf(String.valueOf(firstDayOfMonth)), java.sql.Date.valueOf(String.valueOf(lastDayOfMonth)), branchId);
            } else {
                prescriptions = prescriptionDtoService.findPrescriptionByBillingBranchAndSubmissionDate(java.sql.Date.valueOf(String.valueOf(firstDayOfMonth)), java.sql.Date.valueOf(String.valueOf(lastDayOfMonth)), branchId);
            }
            for (PrescriptionDto rx : prescriptions) {
                if (rx.getDeviceType() != null && rx.getOrthoticOrProsthetic() != null) {
                    String projectedDeviceType = rx.getOrthoticOrProsthetic();
                    List<InsuranceVerification_L_CodeDto> ivlcs = insuranceVerificationDtoRepository.getTotalAllowableByPrescriptionId(rx.getId());
                    for (InsuranceVerification_L_CodeDto iv : ivlcs) {
                        projectedBilledAmount = projectedBilledAmount.add(iv.getTotalAllowable());
                        allBranchesProjected = allBranchesProjected.add(iv.getTotalAllowable());
                        if (projectedDeviceType.equals("orthotic")) {
                            projectedOrthoticAllowable = projectedOrthoticAllowable.add(iv.getTotalAllowable());
                            allBranchesProjectedOrthoticAllowable = allBranchesProjectedOrthoticAllowable.add(iv.getTotalAllowable());
                        } else if (projectedDeviceType.equals("pedorthic")) {
                            projectedPedorthicAllowable = projectedPedorthicAllowable.add(iv.getTotalAllowable());
                            allBranchesProjectedPedorthicAllowable = allBranchesProjectedPedorthicAllowable.add(iv.getTotalAllowable());
                        } else if (projectedDeviceType.equals("misc")) {
                            projectedMiscAllowable = projectedMiscAllowable.add(iv.getTotalAllowable());
                            allBranchesProjectedMiscAllowable = allBranchesProjectedMiscAllowable.add(iv.getTotalAllowable());
                        } else if (projectedDeviceType.equals("prosthetic")) {
                            projectedProstheticAllowable = projectedProstheticAllowable.add(iv.getTotalAllowable());
                            allBranchesProjectedProstheticAllowable = allBranchesProjectedProstheticAllowable.add(iv.getTotalAllowable());
                        } else if (projectedDeviceType.equals("mastectomy")) {
                            projectedMastectomyAllowable = projectedMastectomyAllowable.add(iv.getTotalAllowable());
                            allBranchesProjectedMastectomyAllowable = allBranchesProjectedMastectomyAllowable.add(iv.getTotalAllowable());
                        }
                    }
                } else {
                    String message = "Error - (getProjectedVsBilled) Device Type is null for prescription id = " + rx.getId() + ", for patient id = " + rx.getPatientId() + ", for tenant = " + TenantContext.getCurrentTenant() + ".  Prescription is being excluded from the calculation.";
                    checkForError = message;
                    log.error(checkForError);
                }

            }

            // NEW - Actual Billed Amount by prescription claim first submission date. (Uses same query as AR Report and Billings by Practitioner)
            // Switching to pull from GL
            List<PrescriptionDto> prescriptionsBilled;
            if (isPatientBranch) {
                prescriptionsBilled = prescriptionDtoService.findPrescriptionGLByPatientBranchAndSubmissionDate(java.sql.Date.valueOf(String.valueOf(firstDayOfMonth)), java.sql.Date.valueOf(String.valueOf(lastDayOfMonth)), branchId);
            } else if (isPrescriptionBranch) {
                prescriptionsBilled = prescriptionDtoService.findPrescriptionGLByPrescriptionBranchAndSubmissionDate(java.sql.Date.valueOf(String.valueOf(firstDayOfMonth)), java.sql.Date.valueOf(String.valueOf(lastDayOfMonth)), branchId);
            } else {
                prescriptionsBilled = prescriptionDtoService.findPrescriptionGLByBillingBranchAndSubmissionDate(java.sql.Date.valueOf(String.valueOf(firstDayOfMonth)), java.sql.Date.valueOf(String.valueOf(lastDayOfMonth)), branchId);
            }
            for (PrescriptionDto rx : prescriptionsBilled) {
                if (rx.getDeviceType() != null && rx.getOrthoticOrProsthetic() != null) {
                    String projectedDeviceType = rx.getOrthoticOrProsthetic();

                    BigDecimal allowable = rx.getTotalAllowable().negate();

                    if (projectedDeviceType.equals("orthotic")) {
                        billedOrthoticAllowable = billedOrthoticAllowable.add(allowable);
                        allBranchesBilledOrthoticAllowable = allBranchesBilledOrthoticAllowable.add(allowable);
                        billedAmount = billedAmount.add(allowable);
                        allBranchesBilled = allBranchesBilled.add(allowable);
                    } else if (projectedDeviceType.equals("prosthetic")) {
                        billedProstheticAllowable = billedProstheticAllowable.add(allowable);
                        allBranchesBilledProstheticAllowable = allBranchesBilledProstheticAllowable.add(allowable);
                        billedAmount = billedAmount.add(allowable);
                        allBranchesBilled = allBranchesBilled.add(allowable);
                    } else if (projectedDeviceType.equals("pedorthic")) {
                        billedPedorthicAllowable = billedPedorthicAllowable.add(allowable);
                        allBranchesBilledPedorthicAllowable = allBranchesBilledPedorthicAllowable.add(allowable);
                        billedAmount = billedAmount.add(allowable);
                        allBranchesBilled = allBranchesBilled.add(allowable);
                    } else if (projectedDeviceType.equals("misc")) {
                        billedMiscAllowable = billedMiscAllowable.add(allowable);
                        allBranchesBilledMiscAllowable = allBranchesBilledMiscAllowable.add(allowable);
                        billedAmount = billedAmount.add(allowable);
                        allBranchesBilled = allBranchesBilled.add(allowable);
                    } else if (projectedDeviceType.equals("mastectomy")) {
                        billedMastectomyAllowable = billedMastectomyAllowable.add(allowable);
                        allBranchesBilledMastectomyAllowable = allBranchesBilledMastectomyAllowable.add(allowable);
                        billedAmount = billedAmount.add(allowable);
                        allBranchesBilled = allBranchesBilled.add(allowable);
                    }

                } else {
                    String message = "Error - (getProjectedVsBilled) Device Type is null for prescription id = " + rx.getId() + ", for patient id = " + rx.getPatientId() + ", for tenant = " + TenantContext.getCurrentTenant() + ".  Prescription is being excluded from the calculation.";
                    checkForError = message;
                    log.error(checkForError);
                }

            }
            totalsMap.put("branchName", branch.getName());
            totalsMap.put("projected", projectedBilledAmount.toString());
            totalsMap.put("billed", billedAmount.toString());
            totalsMap.put("billedMastectomyAllowable", billedMastectomyAllowable.toString());
            totalsMap.put("billedProstheticAllowable", billedProstheticAllowable.toString());
            totalsMap.put("billedOrthoticAllowable", billedOrthoticAllowable.toString());
            totalsMap.put("billedMiscAllowable", billedMiscAllowable.toString());
            totalsMap.put("billedPedorthicAllowable", billedPedorthicAllowable.toString());
            totalsMap.put("projectedMastectomyAllowable", projectedMastectomyAllowable.toString());
            totalsMap.put("projectedProstheticAllowable", projectedProstheticAllowable.toString());
            totalsMap.put("projectedOrthoticAllowable", projectedOrthoticAllowable.toString());
            totalsMap.put("projectedPedorthicAllowable", projectedPedorthicAllowable.toString());
            totalsMap.put("projectedMiscAllowable", projectedMiscAllowable.toString());

            if (branch.getMonthlySalesGoal() != null) {
                totalsMap.put("branchMonthlySalesGoal", branch.getMonthlySalesGoal().toString());
                allBranchesMonthlySalesGoal = allBranchesMonthlySalesGoal.add(branch.getMonthlySalesGoal());
            } else {
                totalsMap.put("branchMonthlySalesGoal", "0");
            }
            branchList.add(totalsMap);
        }

        if (branchIds.size() > 1) {
            Map<String, String> totalsMap = new HashMap<>();
            totalsMap.put("branchName", "All Branches");
            totalsMap.put("projected", allBranchesProjected.toString());
            totalsMap.put("billed", allBranchesBilled.toString());
            totalsMap.put("branchMonthlySalesGoal", allBranchesMonthlySalesGoal.toString());
            totalsMap.put("billedMastectomyAllowable", allBranchesBilledMastectomyAllowable.toString());
            totalsMap.put("billedProstheticAllowable", allBranchesBilledProstheticAllowable.toString());
            totalsMap.put("billedOrthoticAllowable", allBranchesBilledOrthoticAllowable.toString());
            totalsMap.put("billedPedorthicAllowable", allBranchesBilledPedorthicAllowable.toString());
            totalsMap.put("billedMiscAllowable", allBranchesBilledMiscAllowable.toString());
            totalsMap.put("projectedMastectomyAllowable", allBranchesProjectedMastectomyAllowable.toString());
            totalsMap.put("projectedProstheticAllowable", allBranchesProjectedProstheticAllowable.toString());
            totalsMap.put("projectedOrthoticAllowable", allBranchesProjectedOrthoticAllowable.toString());
            totalsMap.put("projectedPedorthicAllowable", allBranchesProjectedPedorthicAllowable.toString());
            totalsMap.put("projectedMiscAllowable", allBranchesProjectedMiscAllowable.toString());
            branchList.add(totalsMap);
        }
        return branchList;
    }


    private RxSalesReport getSalesSummaryPerDeviceType(RxSalesReport rxSalesReport, String deviceType) {
        List<IRxSales> data = rxSalesReport.getRxSales();
        RxSalesReport rxSalesReportDeviceType = new RxSalesReport();

        List<IRxSales> deviceTypeData = data.stream().filter(x -> x.getDeviceType().equalsIgnoreCase(deviceType)).collect(Collectors.toList());
        rxSalesReportDeviceType.setRxSales(deviceTypeData);

        List<BigDecimal> totalAllowable = deviceTypeData.stream().map(IRxSales::getAllowable).toList();
        rxSalesReportDeviceType.setTotalAllowable(totalAllowable.stream().reduce(BigDecimal.ZERO, (subtotal, sale) -> subtotal.add(sale)));

        List<BigDecimal> totalBillable = deviceTypeData.stream().map(IRxSales::getBillable).toList();
        rxSalesReportDeviceType.setTotalBillable(totalBillable.stream().reduce(BigDecimal.ZERO, BigDecimal::add));

        List<BigDecimal> totalContractual = deviceTypeData.stream().map(IRxSales::getExpectedContractual).toList();
        rxSalesReportDeviceType.setTotalContractual(totalContractual.stream().reduce(BigDecimal.ZERO, BigDecimal::add));

        return rxSalesReportDeviceType;

    }


    public ResponseEntity<?> deleteClaim(Long claimId) {
        String msg;
        HttpStatus status = HttpStatus.FORBIDDEN;

        // Queries are in the conditional expressions in order to avoid unnecessary DB calls
        if (paymentService.findByClaimId(claimId).size() > 0) {
            msg = "You cannot delete a claim that has an attached payment";
        } else if (appliedPaymentService.findByClaimId(claimId).size() > 0) {
            msg = "You cannot delete a claim that has a payment applied to it";
        } else if (claimSubmissionService.findByClaimId(claimId).size() > 0) {
            msg = "You cannot delete a claim that has been submitted";
        } else if (claimFileService.findByClaimId(claimId).size() > 0) {
            msg = "This claim cannot be deleted due to an existing claim file with no claim submission. " +
                    "Please contact Nymbl support for further assistance.";
        } else if (autoPostPatientService.findByClaimId(claimId).size() > 0) {
            msg = "You cannot delete a claim that has an attached ERA payment.";
        } else if (autoPostClaimResponseService.getAllByClaimId(claimId).size() > 0) {
            msg = "You cannot delete a claim that has an attached claim response.";
        } else if (patientStatementService.findAllByClaimIdIn(Collections.singletonList(claimId)).size() > 0) {
            msg = "You cannot delete a claim that has an attached patient statement";
        } else if (claimRepository.getClaimIsAutoGeneratedCRT(claimId)) {
            msg = "You cannot delete a claim that was auto-generated for a CRT rental";
        } else if (getClaimIsRentalParentById(claimId)) {
            msg = "You cannot delete a claim that has a child rental claim.";
        } else {
            List<NymblStatusHistory> nymblStatusHistoryListForClaim = nymblStatusHistoryService.findByClaimId(claimId);
            nymblStatusHistoryService.deleteInBatch(nymblStatusHistoryListForClaim);
            List<Task> tasksForClaim = taskService.findByClaimId(claimId);
            taskService.deleteInBatch(tasksForClaim);
            prescriptionService.resetRentalByClaimId(claimId);
            delete(claimId);
            msg = "This claim has been deleted";
            status = HttpStatus.OK;
        }
        return ResponseEntity.status(status).body(msg);
    }

    public List<Claim> getPatientRefundClaims(Long branchId) {
        List<Claim> claims = claimRepository.getPatientRefundClaimListByBranch(branchId);
        return claims;
    }

    public void updateClaimTotalsWithoutSubmissionAndAppliedPayments(List<PatientInsuranceDTO> ptInsuranceList, List<InsuranceVerification_L_Code> existingIvlcBillAllowables, boolean isCarrierTypeUpdate) {
        Long pi1 = null;
        Long pi2 = null;
        // In theory, all insurances belong to the same prescription
        Long prescriptionId = null;
        for (PatientInsuranceDTO x : ptInsuranceList) {
            InsuranceVerification iv = x.getInsuranceVerification();
            if (iv != null) {
                insuranceVerificationService.save(iv);
                switch (iv.getCarrierType()) {
                    case "primary":
                        pi1 = iv.getPatientInsuranceId();
                        break;
                    case "secondary":
                        pi2 = iv.getPatientInsuranceId();
                        break;
                    default:
                        break;
                }
            }
            Long pid = iv.getPrescriptionId();
            if (prescriptionId == null) {
                if (pid != null && pid > 0) prescriptionId = pid;
            } else if (!prescriptionId.equals(pid)) {
                log.error("Insurance verifications belong to different prescriptions");
            }
        }
        if (prescriptionId != null && prescriptionId > 0) {
            updateClaimPatientInsuranceIds(prescriptionId, pi1, pi2);
        }

        List<Claim> updateClaimsList = new ArrayList<>();
        List<InsuranceVerification_L_Code> newIvlcList = getPrimaryIvlcListFromPatientInsuranceDtos(ptInsuranceList);
        Map<String, BigDecimal> updatedTotalsMap = calculateClaimTotals(newIvlcList);
        // This was added to allow non carrier type updates to update HCPCS codes for a possible resubmission and we allow manual updates of balance
        if (isCarrierTypeUpdate) {
            updateClaimsList = claimRepository.getClaimsToUpdateByPrescriptionNotHavingClaimSubmissionsOrAppliedPayments(ptInsuranceList.get(0).getInsuranceVerification().getPrescriptionId());
            for (Claim c : updateClaimsList) {
                c.setTotalClaimAmount(updatedTotalsMap.get("billable"));
                if (c.getTotalClaimBalance().compareTo(BigDecimal.ZERO) != 0 ||
                        (c.getTotalClaimBalance().compareTo(BigDecimal.ZERO) == 0 && c.getTotalPtResponsibilityAmount().compareTo(BigDecimal.ZERO) == 0)) {
                    c.setTotalClaimBalance(updatedTotalsMap.get("billable")); // SCRUM-4395
                } else if (c.getTotalPtResponsibilityBalance().compareTo(BigDecimal.ZERO) != 0) {
                    c.setTotalPtResponsibilityBalance(updatedTotalsMap.get("allowable"));
                } else {
                    c.setTotalClaimBalance(updatedTotalsMap.get("billable")); // SCRUM-4395
                }
                save(c);
            }
        } else {
            updateClaimsList = claimRepository.getClaimsByPrescriptionIdWithoutSubmissionOrAppliedPayments(ptInsuranceList.get(0).getInsuranceVerification().getPrescriptionId());
            Map<String, BigDecimal> existingTotalsMap = calculateClaimTotals(existingIvlcBillAllowables);
            BigDecimal allowableDiff = updatedTotalsMap.get("allowable").subtract(existingTotalsMap.get("allowable"));
            for (Claim c : updateClaimsList) {
                c.setTotalClaimAmount(updatedTotalsMap.get("billable"));
                if ((c.getTotalClaimBalance().compareTo(BigDecimal.ZERO) == 0 && c.getTotalPtResponsibilityAmount().compareTo(BigDecimal.ZERO) == 0)) {
                    c.setTotalClaimBalance(c.getTotalClaimBalance().add(allowableDiff));
                } else if (c.getTotalPtResponsibilityBalance().compareTo(BigDecimal.ZERO) != 0) {
                    c.setTotalPtResponsibilityBalance(c.getTotalPtResponsibilityBalance().add(allowableDiff));
                } else {
                    c.setTotalClaimBalance(updatedTotalsMap.get("billable")); // SCRUM-3239
                }
                save(c);
            }
        }
    }

    public void updatePrimaryInsuranceClaimWithOtherPatientInsurance(List<PatientInsuranceDTO> dtoList) {
        for (PatientInsuranceDTO patientInsurance : dtoList) {
            if (patientInsurance.getFirstClaimSubmission() != null && patientInsurance.getFirstClaimSubmission().getClaimId() != null) {
                Claim claim = findOne(patientInsurance.getFirstClaimSubmission().getClaimId());
                if (claim != null) {
                    PatientInsurance otherPatientInsurance = generateOtherPatientInsurance(patientInsurance.getInsuranceVerification());
                    claim.setOtherPatientInsuranceId(otherPatientInsurance != null ? otherPatientInsurance.getId() : null);
                    save(claim);
                }
            }
        }
    }

    List<InsuranceVerification_L_Code> getPrimaryIvlcListFromPatientInsuranceDtos(List<PatientInsuranceDTO> ptInsuranceList) {
        List<InsuranceVerification_L_Code> greatestIvlcList = new ArrayList<>();
        Map<String, List<InsuranceVerification_L_Code>> mapOfVerification = new HashMap<>();
        for (PatientInsuranceDTO p : ptInsuranceList) {
            mapOfVerification.put(p.getInsuranceVerification().getCarrierType(), p.getInsuranceVerificationLCodes());
        }
        if (mapOfVerification.get("primary") != null) {
            greatestIvlcList = mapOfVerification.get("primary");
        } else if (mapOfVerification.get("secondary") != null) {
            greatestIvlcList = mapOfVerification.get("secondary");
        } else if (mapOfVerification.get("tertiary") != null || mapOfVerification.get("quaternary") != null
                || mapOfVerification.get("quinary") != null || mapOfVerification.get("senary") != null
                || mapOfVerification.get("septenary") != null || mapOfVerification.get("octonary") != null
                || mapOfVerification.get("nonary") != null || mapOfVerification.get("denary") != null) {
            greatestIvlcList = mapOfVerification.get("other");
        }
        return greatestIvlcList;
    }

    Map<String, BigDecimal> calculateClaimTotals(List<InsuranceVerification_L_Code> ivlcList) {
        Map<String, BigDecimal> result = new HashMap<>();
        BigDecimal billable = BigDecimal.ZERO;
        BigDecimal allowable = BigDecimal.ZERO;
        for (InsuranceVerification_L_Code ivlc : ivlcList) {
            billable = billable.add(ivlc.getTotalCharge());
            allowable = allowable.add(ivlc.getTotalAllowable());
        }
        result.put("billable", billable);
        result.put("allowable", allowable);
        return result;
    }

    private String getPayerName(PatientInsurance patientInsurance, String useIcbNameForHcfa) {
        String payerName = null;
        if (patientInsurance.getInsuranceCompanyBranch() != null && useIcbNameForHcfa != null && useIcbNameForHcfa.equals("Y")) {
            payerName = patientInsurance.getInsuranceCompanyBranch().getName();
        } else if (patientInsurance.getInsuranceCompany() != null) {
            payerName = patientInsurance.getInsuranceCompany().getName();
        }
        if (payerName == null && patientInsurance.getInsuranceCompanyBranch() == null && useIcbNameForHcfa != null && useIcbNameForHcfa.equals("Y")) {
            String message = "Error - Patient insurance company branch is null for patient id = " + patientInsurance.getPatient().getId() +
                    " for patient insurance id = " + patientInsurance.getId() +
                    " for tenant = " + TenantContext.getCurrentTenant();
            log.error(message);
        }
        return payerName;
    }

    @Auditable(entry = "New Claim Added")
    public void auditNewlyAddedClaim(List<Claim> claim) {
    }

    @Auditable(entry = "Claim Deleted")
    public void auditDeleteClaim(Claim claim) {
    }

    public String getCheckForError() {
        return checkForError;
    }

    public IClaimTotals getClaimTotals(Long claimId, Long rxId) {
        IClaimTotals result = paymentService.getClaimTotalsByClaimIdAndPrescriptionId(claimId, rxId);
        return result;
    }

    public boolean getClaimIsRentalParentById(Long claimId) {
        boolean result = false;
        List<Prescription> children = prescriptionService.getRentalChildPrescriptionsByClaimId(claimId);
        if (children.size() > 0 && children.get(0) != null)
            result = true;
        return result;
    }

    /**
     * Update claim to reflect the primary and/or secondary insurance change
     *
     * @param prescriptionId
     * @param primaryPatientInsuranceId
     * @param otherPatientInsuranceId
     */
    public void updateClaimPatientInsuranceIds(Long prescriptionId, Long primaryPatientInsuranceId, Long otherPatientInsuranceId) {
        List<Claim> claims = findByPrescriptionId(prescriptionId);
        Claim claim = null;
        if (claims != null && claims.size() > 0) {
            for (Claim c : claims) {
                if (claim == null || claim.getId() < c.getId()) claim = c;
            }
        }
        if (claim == null) return;
        // In a claim primary insurance is never null
        Long pi1 = claim.getResponsiblePatientInsuranceId();
        Long pi2 = claim.getOtherPatientInsuranceId();
        // Update the claim only if there really are some changes
        if ((primaryPatientInsuranceId != null && primaryPatientInsuranceId > 0 && pi1 != primaryPatientInsuranceId)
                // If new other insurance is valid and in the claim it is invalid or different - replace with the new ID
                || (otherPatientInsuranceId != null && otherPatientInsuranceId > 0 && (pi2 == null || pi2 != otherPatientInsuranceId))
                // If other insurance in the claim is not null, we can make it null
                || (otherPatientInsuranceId == null && pi2 != null)) {
            // update primary insurance only if we have a valid ID
            if (primaryPatientInsuranceId != null && primaryPatientInsuranceId > 0) {
                claim.setPatientInsuranceId(primaryPatientInsuranceId);
                claim.setResponsiblePatientInsuranceId(primaryPatientInsuranceId);
            }
            // Theoretically, other insurance may change from something to null
            claim.setOtherPatientInsuranceId(otherPatientInsuranceId);
            this.save(claim);
        }
    }

    public Claim updateClaimPhysicianInfo(PhysicianBillingEditDTO inComingRequest) {
        Claim claim = findOne(inComingRequest.getClaimId());
        Prescription prescription = claim.getPrescription();
        if("treating_practitioner".equals(inComingRequest.getType())) {
            prescription.setTreatingPractitionerId(inComingRequest.getPhysicianId());
        } else {
            Physician physician = physicianService.findOne(inComingRequest.getPhysicianId());
            if("primary_physician".equals(inComingRequest.getType())) {
                prescription.setPrimaryCarePhysician(physician);
                prescription.setPrimaryCarePhysicianId(inComingRequest.getPhysicianId());
            } else if("referring_physician".equals(inComingRequest.getType())) {
                prescription.setReferringPhysician(physician);
                prescription.setReferringPhysicianId(inComingRequest.getPhysicianId());
            }
        }
        prescriptionService.save(prescription);
        return this.save(claim);
    }
}

