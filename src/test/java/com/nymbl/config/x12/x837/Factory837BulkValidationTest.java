package com.nymbl.config.x12.x837;

import com.imsweb.x12.reader.X12Reader;
import com.imsweb.x12.Loop;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Tests for Factory837 bulk claim validation using the com.imsweb:x12-parser library.
 * This test validates the structure and content of generated bulk X12 files.
 */
@DisplayName("Factory837 Bulk X12 Validation Tests")
class Factory837BulkValidationTest {

    @Test
    @DisplayName("Validate working production X12 file using com.imsweb X12 parser")
    void testValidateActualBulkX12File() {
        // Read the actual working X12 content from x12.txt file (from production test.live)
        String x12Content;
        try {
            x12Content = java.nio.file.Files.readString(java.nio.file.Paths.get("x12.txt"));
            System.out.println("=== DEBUG: X12 CONTENT FROM FILE ===");
            System.out.println("First 200 characters: " + x12Content.substring(0, Math.min(200, x12Content.length())));
            System.out.println("Total length: " + x12Content.length());
        } catch (Exception e) {
            fail("Could not read x12.txt file: " + e.getMessage());
            return;
        }

        // Parse and validate using the actual X12 parser library
        validateX12UsingParser(x12Content);
    }



    /**
     * Validates X12 structure using the com.imsweb:x12-parser library
     */
    private void validateX12UsingParser(String x12Content) {
        try {
            System.out.println("=== X12 VALIDATION USING COM.IMSWEB PARSER ===");

            // Write X12 content to a temporary file and parse it
            java.io.File tempFile = java.io.File.createTempFile("x12_test", ".txt");
            try (java.io.FileWriter writer = new java.io.FileWriter(tempFile)) {
                writer.write(x12Content);
            }

            // Parse the X12 file using the actual library API
            X12Reader reader = new X12Reader(X12Reader.FileType.ANSI837_5010_X222, tempFile);

            System.out.println("✅ X12 file parsed successfully by com.imsweb parser");

            // Check for errors
            List<String> errors = reader.getErrors();
            List<String> fatalErrors = reader.getFatalErrors();

            if (!fatalErrors.isEmpty()) {
                System.out.println("❌ FATAL ERRORS found:");
                for (String error : fatalErrors) {
                    System.out.println("  - " + error);
                }
                fail("X12 file has fatal errors: " + fatalErrors);
            }

            if (!errors.isEmpty()) {
                System.out.println("❌ VALIDATION ERRORS found:");
                for (String error : errors) {
                    System.out.println("  - " + error);
                }
                fail("X12 file has validation errors and is INVALID for submission");
            } else {
                System.out.println("✅ No validation errors found");
            }

            // Get the loops
            List<Loop> loops = reader.getLoops();
            System.out.println("Number of ISA-IEA transactions: " + loops.size());

            System.out.println("✅ X12 837 file is VALID - no errors found");

            // Clean up temp file
            tempFile.delete();

        } catch (Exception e) {
            System.out.println("❌ CRITICAL ERROR: X12 parsing failed!");
            System.out.println("Error: " + e.getMessage());
            e.printStackTrace();
            fail("X12 file failed to parse with com.imsweb parser: " + e.getMessage());
        }
    }




}
