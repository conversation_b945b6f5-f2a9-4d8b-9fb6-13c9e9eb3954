package com.nymbl.config.x12.x837;

import com.imsweb.x12.FileType;
import com.imsweb.x12.Loop;
import com.imsweb.x12.reader.X12Reader;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.io.IOException;
import java.io.StringReader;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Comprehensive validation tests for bulk X12 claim generation using com.imsweb:x12-parser library.
 * This test validates that bulk X12 files are 100% valid according to professional X12 standards.
 */
@SpringBootTest
@ActiveProfiles("test")
public class BulkX12ValidationTest {

    /**
     * Test validates the x12.txt file in the project root using com.imsweb X12 parser.
     * This ensures the bulk X12 generation creates valid files with no warnings or errors.
     */
    @Test
    public void testValidateX12FileWithImsweb() throws IOException {
        // Read the x12.txt file from project root
        String x12Content = Files.readString(Paths.get("x12.txt"));
        
        assertNotNull(x12Content, "X12 file content should not be null");
        assertFalse(x12Content.trim().isEmpty(), "X12 file content should not be empty");
        
        // Validate using com.imsweb X12 parser
        try (StringReader reader = new StringReader(x12Content)) {
            X12Reader x12Reader = new X12Reader(FileType.ANSI837_5010_X222, reader);
            
            // Parse the X12 file
            List<Loop> loops = x12Reader.getLoops();
            
            // Validate that parsing was successful
            assertNotNull(loops, "X12 loops should not be null");
            assertFalse(loops.isEmpty(), "X12 should contain loops");
            
            // Check for any parsing errors or warnings
            List<String> errors = x12Reader.getErrors();
            List<String> warnings = x12Reader.getWarnings();
            
            // Log any issues found
            if (!errors.isEmpty()) {
                System.err.println("X12 Validation Errors:");
                errors.forEach(error -> System.err.println("  ERROR: " + error));
            }
            
            if (!warnings.isEmpty()) {
                System.err.println("X12 Validation Warnings:");
                warnings.forEach(warning -> System.err.println("  WARNING: " + warning));
            }
            
            // Assert no errors or warnings for 100% valid X12
            assertTrue(errors.isEmpty(), 
                "X12 file should have no validation errors. Found: " + String.join(", ", errors));
            assertTrue(warnings.isEmpty(), 
                "X12 file should have no validation warnings. Found: " + String.join(", ", warnings));
            
            // Validate basic X12 structure
            validateX12Structure(loops);
            
            System.out.println("✅ X12 file validation PASSED - 100% valid with no errors or warnings");
            System.out.println("📊 Total loops parsed: " + loops.size());
            
        } catch (Exception e) {
            fail("Failed to parse X12 file with com.imsweb parser: " + e.getMessage(), e);
        }
    }
    
    /**
     * Validates the basic structure of the parsed X12 file.
     */
    private void validateX12Structure(List<Loop> loops) {
        // Find ISA loop (interchange envelope)
        Loop isaLoop = findLoopByName(loops, "ISA");
        assertNotNull(isaLoop, "ISA loop should be present");
        
        // Find GS loop (functional group)
        Loop gsLoop = findLoopByName(loops, "GS");
        assertNotNull(gsLoop, "GS loop should be present");
        
        // Find ST loop (transaction set)
        Loop stLoop = findLoopByName(loops, "ST");
        assertNotNull(stLoop, "ST loop should be present");
        
        // Validate ST segment is 837 (Professional Healthcare Claim)
        String transactionType = getElementValue(stLoop, "ST", 1);
        assertEquals("837", transactionType, "Transaction type should be 837 for healthcare claims");
        
        // Find BHT loop (Beginning of Hierarchical Transaction)
        Loop bhtLoop = findLoopByName(loops, "BHT");
        assertNotNull(bhtLoop, "BHT loop should be present");
        
        // Validate BHT structure code is 0019 (Information Source, Subscriber, Dependent)
        String structureCode = getElementValue(bhtLoop, "BHT", 1);
        assertEquals("0019", structureCode, "BHT structure code should be 0019 for 837P");
        
        System.out.println("✅ X12 structure validation passed");
    }
    
    /**
     * Finds a loop by its name in the list of loops.
     */
    private Loop findLoopByName(List<Loop> loops, String name) {
        return loops.stream()
            .filter(loop -> name.equals(loop.getName()))
            .findFirst()
            .orElse(null);
    }
    
    /**
     * Gets the value of a specific element from a segment within a loop.
     */
    private String getElementValue(Loop loop, String segmentName, int elementIndex) {
        if (loop == null) return null;
        
        try {
            // Access the segment and element using the com.imsweb API
            return loop.getLoop(segmentName).getSegment().getElement(elementIndex);
        } catch (Exception e) {
            return null;
        }
    }
    
    /**
     * Test validates that the X12 file contains required healthcare claim segments.
     */
    @Test
    public void testValidateHealthcareClaimSegments() throws IOException {
        String x12Content = Files.readString(Paths.get("x12.txt"));
        
        try (StringReader reader = new StringReader(x12Content)) {
            X12Reader x12Reader = new X12Reader(FileType.ANSI837_5010_X222, reader);
            List<Loop> loops = x12Reader.getLoops();
            
            // Validate required segments are present
            assertTrue(containsSegment(loops, "NM1"), "Should contain NM1 (Name) segments");
            assertTrue(containsSegment(loops, "HL"), "Should contain HL (Hierarchical Level) segments");
            assertTrue(containsSegment(loops, "PRV"), "Should contain PRV (Provider Information) segments");
            assertTrue(containsSegment(loops, "SBR"), "Should contain SBR (Subscriber Information) segments");
            
            System.out.println("✅ Healthcare claim segments validation passed");
        }
    }
    
    /**
     * Checks if the loops contain a specific segment type.
     */
    private boolean containsSegment(List<Loop> loops, String segmentName) {
        return loops.stream()
            .anyMatch(loop -> segmentName.equals(loop.getName()));
    }
    
    /**
     * Test validates that the X12 file has proper segment counts and structure.
     */
    @Test
    public void testValidateSegmentCounts() throws IOException {
        String x12Content = Files.readString(Paths.get("x12.txt"));
        
        // Count segments manually
        String[] segments = x12Content.split("~");
        int actualSegmentCount = segments.length - 1; // Subtract 1 for the final empty element after last ~
        
        try (StringReader reader = new StringReader(x12Content)) {
            X12Reader x12Reader = new X12Reader(FileType.ANSI837_5010_X222, reader);
            List<Loop> loops = x12Reader.getLoops();
            
            // Find SE segment to get the reported segment count
            Loop seLoop = findLoopByName(loops, "SE");
            assertNotNull(seLoop, "SE (Transaction Set Trailer) segment should be present");
            
            String reportedCount = getElementValue(seLoop, "SE", 1);
            assertNotNull(reportedCount, "SE segment should contain segment count");
            
            int expectedCount = Integer.parseInt(reportedCount);
            
            System.out.println("📊 Actual segments: " + actualSegmentCount);
            System.out.println("📊 Reported in SE: " + expectedCount);
            
            // The SE segment count should match the actual segment count
            // Note: SE count includes the SE segment itself
            assertTrue(Math.abs(actualSegmentCount - expectedCount) <= 1, 
                "Segment count mismatch. Actual: " + actualSegmentCount + ", SE reports: " + expectedCount);
            
            System.out.println("✅ Segment count validation passed");
        }
    }
}
